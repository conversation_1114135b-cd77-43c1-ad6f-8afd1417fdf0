#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
User_Task返航逻辑集成测试
版权信息：米醋电子工作室
创建日期：2025-07-31

功能描述：
测试User_Task.c中返航逻辑集成的正确性和完整性
"""

import re
import os

def analyze_header_includes(file_path):
    """分析头文件包含"""
    print("📄 分析头文件包含")
    print("=" * 50)
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查必要的头文件包含
    required_includes = [
        ('zigbee.h', r'#include\s+"zigbee\.h"'),
        ('path_storage.h', r'#include\s+"path_storage\.h"'),
        ('stdio.h', r'#include\s+<stdio\.h>')
    ]
    
    all_found = True
    for include_name, pattern in required_includes:
        if re.search(pattern, content):
            print(f"   ✅ {include_name}: 头文件包含正确")
        else:
            print(f"   ❌ {include_name}: 头文件包含缺失")
            all_found = False
    
    return all_found

def analyze_global_variables(file_path):
    """分析全局变量定义"""
    print("\n🔧 分析返航路径全局变量")
    print("=" * 50)
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查返航路径相关变量
    required_variables = [
        ('current_return_path_ptr', r'static\s+const\s+u8\*\s+current_return_path_ptr'),
        ('current_return_path_length', r'static\s+int\s+current_return_path_length'),
        ('current_return_step', r'static\s+int\s+current_return_step'),
        ('return_path_loaded', r'static\s+bool\s+return_path_loaded')
    ]
    
    all_found = True
    for var_name, pattern in required_variables:
        if re.search(pattern, content):
            print(f"   ✅ {var_name}: 变量定义正确")
        else:
            print(f"   ❌ {var_name}: 变量定义缺失")
            all_found = False
    
    return all_found

def analyze_helper_functions(file_path):
    """分析辅助函数实现"""
    print("\n🛠️ 分析返航路径辅助函数")
    print("=" * 50)
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查辅助函数
    required_functions = [
        {
            'name': 'convert_return_path_to_coords',
            'signature': r'static\s+void\s+convert_return_path_to_coords\s*\(',
            'description': '返航路径转换函数'
        },
        {
            'name': 'execute_return_path_navigation',
            'signature': r'static\s+void\s+execute_return_path_navigation\s*\(',
            'description': '返航路径导航函数'
        }
    ]
    
    all_found = True
    for func in required_functions:
        if re.search(func['signature'], content):
            print(f"   ✅ {func['description']}: 找到函数实现")
            
            # 检查函数内容的关键要素
            func_pattern = func['signature'] + r'.*?^}'
            func_match = re.search(func_pattern, content, re.MULTILINE | re.DOTALL)
            
            if func_match:
                func_content = func_match.group(0)
                
                # 检查关键实现要素
                if func['name'] == 'convert_return_path_to_coords':
                    checks = [
                        ('参数验证', r'if\s*\([^)]*NULL[^)]*\)'),
                        ('路径保存', r'current_return_path_ptr\s*='),
                        ('状态设置', r'return_path_loaded\s*=\s*true'),
                        ('调试输出', r'AnoPTv8SendStr')
                    ]
                elif func['name'] == 'execute_return_path_navigation':
                    checks = [
                        ('路径检查', r'return_path_loaded'),
                        ('目标点获取', r'target_position_code'),
                        ('坐标查找', r'work_pos\[i\]\[4\]'),
                        ('位置设置', r'set_target_position')
                    ]
                
                for check_name, pattern in checks:
                    if re.search(pattern, func_content):
                        print(f"      ✅ {check_name}: 实现正确")
                    else:
                        print(f"      ❌ {check_name}: 实现缺失")
                        all_found = False
            else:
                print(f"      ❌ 无法解析函数内容")
                all_found = False
        else:
            print(f"   ❌ {func['description']}: 未找到函数实现")
            all_found = False
    
    return all_found

def analyze_case_63_integration(file_path):
    """分析case 63返航逻辑集成"""
    print("\n🎯 分析case 63返航逻辑集成")
    print("=" * 50)
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找case 63
    case_63_pattern = r'case\s+63:.*?break;'
    case_63_match = re.search(case_63_pattern, content, re.DOTALL)
    
    if case_63_match:
        print("   ✅ case 63: 找到返航阶段代码")
        case_63_content = case_63_match.group(0)
        
        # 检查关键集成要素
        integration_checks = [
            ('禁飞区获取', r'zigbee_get_no_fly_zones'),
            ('返航路径查找', r'find_precomputed_return_path_direct'),
            ('路径转换调用', r'convert_return_path_to_coords'),
            ('导航执行调用', r'execute_return_path_navigation'),
            ('降级机制', r'handle_return_home'),
            ('步骤管理', r'current_return_step'),
            ('完成检测', r'current_return_step\s*>=\s*current_return_path_length'),
            ('状态重置', r'return_path_loaded\s*=\s*false')
        ]
        
        all_checks_passed = True
        for check_name, pattern in integration_checks:
            if re.search(pattern, case_63_content):
                print(f"      ✅ {check_name}: 集成正确")
            else:
                print(f"      ❌ {check_name}: 集成缺失")
                all_checks_passed = False
        
        return all_checks_passed
    else:
        print("   ❌ case 63: 未找到返航阶段代码")
        return False

def analyze_initialization(file_path):
    """分析初始化逻辑"""
    print("\n🚀 分析返航路径初始化")
    print("=" * 50)
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找handle_mission_init函数
    init_pattern = r'static\s+void\s+handle_mission_init.*?^}'
    init_match = re.search(init_pattern, content, re.MULTILINE | re.DOTALL)
    
    if init_match:
        print("   ✅ handle_mission_init: 找到初始化函数")
        init_content = init_match.group(0)
        
        # 检查返航路径状态重置
        reset_checks = [
            ('return_path_loaded重置', r'return_path_loaded\s*=\s*false'),
            ('current_return_path_ptr重置', r'current_return_path_ptr\s*=\s*NULL'),
            ('current_return_step重置', r'current_return_step\s*=\s*0'),
            ('初始化调试信息', r'Return path system initialized')
        ]
        
        all_reset = True
        for check_name, pattern in reset_checks:
            if re.search(pattern, init_content):
                print(f"      ✅ {check_name}: 重置正确")
            else:
                print(f"      ❌ {check_name}: 重置缺失")
                all_reset = False
        
        return all_reset
    else:
        print("   ❌ handle_mission_init: 未找到初始化函数")
        return False

def analyze_code_quality(file_path):
    """分析代码质量"""
    print("\n📊 代码质量分析")
    print("=" * 50)
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 代码质量检查
    quality_metrics = {
        '中文注释': len(re.findall(r'//.*[\u4e00-\u9fff]', content)),
        '函数注释': len(re.findall(r'/\*\*.*?\*/', content, re.DOTALL)),
        '调试输出': len(re.findall(r'AnoPTv8SendStr', content)),
        '错误处理': len(re.findall(r'if\s*\([^)]*NULL[^)]*\)', content)),
        '状态检查': len(re.findall(r'return_path_loaded', content))
    }
    
    for metric, count in quality_metrics.items():
        print(f"   📈 {metric}: {count} 处")
    
    # 检查编码规范
    print("\n   📋 编码规范检查:")
    encoding_checks = [
        ('UTF-8编码', '✅' if '米醋电子工作室' in content else '❌'),
        ('函数命名规范', '✅' if re.search(r'convert_return_path_to_coords', content) else '❌'),
        ('变量命名规范', '✅' if re.search(r'current_return_step', content) else '❌'),
        ('注释完整性', '✅' if re.search(r'返航路径', content) else '❌')
    ]
    
    for check_name, status in encoding_checks:
        print(f"      {status} {check_name}")

def main():
    """主测试函数"""
    print("🚀 User_Task返航逻辑集成测试")
    print("版权：米醋电子工作室")
    print("=" * 70)
    
    # 文件路径
    user_task_file = "../../FcSrc/User_Task.c"
    
    try:
        # 分析头文件包含
        includes_ok = analyze_header_includes(user_task_file)
        
        # 分析全局变量
        variables_ok = analyze_global_variables(user_task_file)
        
        # 分析辅助函数
        functions_ok = analyze_helper_functions(user_task_file)
        
        # 分析case 63集成
        case_63_ok = analyze_case_63_integration(user_task_file)
        
        # 分析初始化逻辑
        init_ok = analyze_initialization(user_task_file)
        
        # 代码质量分析
        analyze_code_quality(user_task_file)
        
        print("\n" + "=" * 70)
        
        # 总结
        if includes_ok and variables_ok and functions_ok and case_63_ok and init_ok:
            print("🎉 所有检查通过! User_Task返航逻辑集成完成")
            print("📋 集成摘要:")
            print("   ✅ 头文件包含: path_storage.h已正确包含")
            print("   ✅ 全局变量: 4个返航路径状态变量已定义")
            print("   ✅ 辅助函数: 2个返航路径处理函数已实现")
            print("   ✅ 状态机集成: case 63已完整集成预计算返航路径")
            print("   ✅ 初始化逻辑: 返航路径状态重置已添加")
            print("   ✅ 降级机制: 保留直线返航作为备用方案")
        else:
            print("❌ 检查发现问题，需要进一步修复")
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
