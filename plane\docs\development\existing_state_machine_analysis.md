# 现有状态机结构和数据流分析报告

## 📋 概述

本文档深入分析User_Task.c中execute_mission_state_machine()函数的现有实现，为野生动物巡查状态机重构提供技术基础。

## 🔍 状态机架构分析

### 1. 状态机结构

```c
static void execute_mission_state_machine(void)
{
    static u16 mission_timer_ms = 0;
    
    switch(mission_step) {
        case 0: // 初始化阶段
        case 1: // 获取起始位置和计算工作点
        case 2: // 延时等待
        case 3: // 返回起始点
        case 4: // 悬停等待
        case 5-33: // 工作点导航（货物遍历逻辑）
        case 34: // 降落
    }
}
```

**关键特征**：
- 使用静态定时器`mission_timer_ms`进行时间管理
- 状态5-33为工作点导航阶段，采用线性索引方式：`current_work_point = mission_step - 5`
- 每个状态都有明确的转换条件和处理逻辑

### 2. 工作点数据结构

#### work_pos[63][7] - 理想工作点模板
```c
//work_pos[i][0-6] = {x, y, z, yaw, position_code, order, status}
//字段定义：
//  [0] x坐标 (cm)
//  [1] y坐标 (cm)  
//  [2] z高度 (cm)
//  [3] yaw角度 (度)
//  [4] position_code (位置编码，行号×10+列号)
//  [5] order (执行顺序，1-63，用于路径规划)
//  [6] status (状态标记，0=正常，1=禁飞区)
```

**数据示例**：
- A9B1: `{0, 0, 120, 0, 91, 0, 0}` (索引0)
- A9B2: `{50, 0, 120, 0, 92, 0, 0}` (索引1)
- A8B1: `{0, 50, 120, 0, 81, 0}` (索引7)

#### actual_work_pos[63][4] - 实际工作坐标
```c
s16 actual_work_pos[WORK_POINT_ARRAY_SIZE][4]; // 加上漂移值后，实际工作的点
```

**计算逻辑**：
```c
static void calculate_actual_work_positions(void)
{
    for (int i = 0; i < WORK_POINT_ARRAY_SIZE; i++) {
        if (is_work_point_valid(i)) {
            // 应用漂移到x和y坐标，z和yaw保持不变
            actual_work_pos[i][0] = work_pos[i][0] + home_pos[0];
            actual_work_pos[i][1] = work_pos[i][1] + home_pos[1];
            actual_work_pos[i][2] = work_pos[i][2];
            actual_work_pos[i][3] = work_pos[i][3];
        }
    }
}
```

### 3. 位置检测机制

#### 位置到达检测函数
```c
// XY位置检测 - 基于PID输出判断
bool is_position_reached(void) {
    return (ABS(PID_V[0]) < RC_POSITION_THRESHOLD && ABS(PID_V[1]) < RC_POSITION_THRESHOLD);
}

// 偏航角检测
bool is_yaw_reached(void) {
    return (ABS(PID_V[3]) < RC_YAW_THRESHOLD);
}

// Z轴位置检测
inline bool is_z_position_reached(void) {
    return (ABS(PID_V[2]) < RC_POSITION_THRESHOLD);
}
```

**阈值设置**：
- `RC_POSITION_THRESHOLD = 5` (位置到达阈值)
- `RC_YAW_THRESHOLD = 3` (偏航角到达阈值)
- `RC_MISSION_DELAY_MS = 60` (任务延时时间)

### 4. 导航控制机制

#### handle_work_point_navigation()函数
```c
void handle_work_point_navigation(u8 work_point_index)
{
    set_target_position(
        actual_work_pos[work_point_index][0],
        actual_work_pos[work_point_index][1],
        actual_work_pos[work_point_index][2],
        actual_work_pos[work_point_index][3]
    );
    enable_position_control(true, true, true, true);
}
```

**控制流程**：
1. 设置目标位置到`target_pos[4]`数组
2. 启用所有控制环（XY、Z、Yaw）
3. PID控制器自动调节到目标位置

### 5. 工作点导航状态逻辑

```c
case 5-33: // 工作点导航阶段
{
    u8 current_work_point = mission_step - 5;
    handle_work_point_navigation(current_work_point);
    
    // 位置到达检测
    if (is_position_reached() && is_yaw_reached() && is_z_position_reached()) {
        // 稳定延时
        if (!handle_wait(&mission_timer_ms, RC_MISSION_DELAY_MS)) {
            return;
        }
        
        // 处理不同位置类型
        if (is_qr_detection_position(current_work_point)) {
            LED_f = 1; // 激活LED
            // 动物识别功能已集成到Maixcam.c中
        }
        
        mission_step += 1; // 转到下一个工作点
    }
}
```

## 🔗 Path_Planner.c集成分析

### 1. plan_optimal_path()接口
```c
int plan_optimal_path(s16 work_pos[][7], int point_count,
                     int *no_fly_zones, int no_fly_count,
                     const path_planner_config_t *config,
                     path_planner_stats_t *stats)
```

**功能特点**：
- 输入：work_pos数组、禁飞区数组、配置参数
- 输出：在work_pos[i][5]字段设置order值（1-63）
- 算法：贪心算法 + 2-opt优化
- 起始点：默认A9B1（索引0）

### 2. order字段设置机制
```c
// 在贪心算法中设置执行顺序
work_pos[current_idx][5] = path_length;  // order从1开始
```

**当前问题**：
- 现有状态机未使用Path_Planner的order字段
- 采用线性索引方式：mission_step - 5
- 无法跳过禁飞区，无法实现最优路径

## 📊 关键发现

### 1. 可复用的组件
✅ **完全可复用**：
- `handle_work_point_navigation()` - 导航控制
- `is_position_reached()` 系列函数 - 位置检测
- `handle_wait()` - 延时等待
- `set_target_position()` - 位置设置
- `enable_position_control()` - 控制环管理
- `calculate_actual_work_positions()` - 坐标计算

### 2. 需要重构的部分
❌ **需要重写**：
- 状态5-33的线性工作点访问逻辑
- 缺少与Path_Planner的集成
- 缺少按order字段顺序访问的机制
- 缺少禁飞区跳过逻辑

### 3. 数据流架构
```
work_pos[63][7] (理想坐标模板)
        ↓
home_pos[4] (起始位置漂移)
        ↓
actual_work_pos[63][4] (实际工作坐标)
        ↓
target_pos[4] (当前目标位置)
        ↓
PID控制器 → 飞行器控制
```

## 🎯 重构建议

### 1. 新状态机架构
- 状态2：集成路径规划调用
- 状态3：获取下一个巡查点（按order字段）
- 状态4：导航到巡查点
- 状态5：悬停识别（激光笔+动物识别）
- 状态6：检查巡查完成状态

### 2. 关键新增函数
```c
int get_next_patrol_point(void);     // 按order获取下一个巡查点
bool is_patrol_complete(void);       // 检查巡查完成状态
```

### 3. Path_Planner集成点
- 在状态2调用`plan_optimal_path()`
- 使用返回的order字段进行有序访问
- 自动跳过status=1的禁飞区点

## ✅ 分析结论

现有状态机具有良好的基础架构和完整的辅助函数，主要问题是工作点访问逻辑需要从线性索引改为基于Path_Planner的order字段访问。通过重构状态5-33的逻辑，可以实现与路径规划算法的完美集成，为野生动物巡查系统提供最优的巡查路径。
