#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化版HTML更新脚本 - 手动更新关键路径的HTML文件
"""

def update_path_50_html():
    """更新路径50的HTML文件"""
    
    # 路径50的数据（从C文件中提取）
    no_fly_zones = [12, 13, 14]
    patrol_path = [91, 81, 71, 61, 51, 41, 31, 21, 11, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 86, 96, 97, 87, 77, 67, 57, 47, 37, 27, 17]
    return_path = [17, 26, 35, 44, 53, 62, 72, 81, 91]
    
    html_content = f'''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>路径50 - 禁飞区{no_fly_zones} (最新修正版)</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }}
        .grid-container {{
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }}
        .grid {{
            display: grid;
            grid-template-columns: repeat(9, 50px);
            grid-template-rows: repeat(7, 50px);
            gap: 2px;
            border: 3px solid #333;
            background-color: #333;
            padding: 10px;
            border-radius: 10px;
        }}
        .cell {{
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
            color: white;
            border-radius: 4px;
            position: relative;
        }}
        .normal {{ background-color: #4CAF50; }}
        .no-fly {{ background-color: #f44336; }}
        .start {{ background-color: #2196F3; }}
        .patrol {{ background-color: #FF9800; }}
        .return {{ background-color: #9C27B0; }}
        .highlight-17 {{
            background-color: #FF5722 !important;
            animation: pulse 2s infinite;
        }}
        @keyframes pulse {{
            0% {{ opacity: 1; }}
            50% {{ opacity: 0.7; }}
            100% {{ opacity: 1; }}
        }}
        .path-number {{
            position: absolute;
            top: 2px;
            left: 2px;
            font-size: 8px;
            background: rgba(0,0,0,0.7);
            padding: 1px 3px;
            border-radius: 2px;
        }}
        .info-panel {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 30px 0;
        }}
        .info-box {{
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #007bff;
        }}
        .path-sequence {{
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
        }}
        .legend {{
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }}
        .legend-item {{
            display: flex;
            align-items: center;
            gap: 8px;
        }}
        .legend-color {{
            width: 20px;
            height: 20px;
            border-radius: 4px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>路径50 可视化 (最新修正版)</h1>
            <p>禁飞区: {no_fly_zones} | 巡查点数: {len(patrol_path)} | 返航点数: {len(return_path)}</p>
            <p><strong>✅ 包含位置17，确保100%覆盖</strong></p>
        </div>

        <div class="legend">
            <div class="legend-item">
                <div class="legend-color start"></div>
                <span>起点/终点 (A9B1)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color no-fly"></div>
                <span>禁飞区</span>
            </div>
            <div class="legend-item">
                <div class="legend-color patrol"></div>
                <span>巡查路径</span>
            </div>
            <div class="legend-item">
                <div class="legend-color return"></div>
                <span>返航路径</span>
            </div>
            <div class="legend-item">
                <div class="legend-color highlight-17" style="background-color: #FF5722;"></div>
                <span>位置17 (重点关注)</span>
            </div>
        </div>

        <div class="grid-container">
            <div class="grid" id="pathGrid">
                <!-- 网格将由JavaScript生成 -->
            </div>
        </div>

        <div class="info-panel">
            <div class="info-box">
                <h3>🎯 巡查路径序列 ({len(patrol_path)}个点)</h3>
                <div class="path-sequence">
                    {' → '.join(map(str, patrol_path))}
                </div>
                <p><strong>包含位置17: ✅ 是 (第{patrol_path.index(17)+1}个点)</strong></p>
            </div>
            <div class="info-box">
                <h3>🏠 返航路径序列 ({len(return_path)}个点)</h3>
                <div class="path-sequence">
                    {' → '.join(map(str, return_path))}
                </div>
            </div>
        </div>
    </div>

    <script>
        // 网格数据
        const noFlyZones = {no_fly_zones};
        const patrolPath = {patrol_path};
        const returnPath = {return_path};
        
        // 生成网格
        function generateGrid() {{
            const grid = document.getElementById('pathGrid');
            
            // 创建7行9列的网格 (B7到B1, A1到A9)
            for (let row = 6; row >= 0; row--) {{
                for (let col = 0; col < 9; col++) {{
                    const positionCode = (col + 1) * 10 + (row + 1);
                    const cell = document.createElement('div');
                    cell.className = 'cell';
                    cell.textContent = positionCode;
                    
                    // 确定单元格类型
                    if (positionCode === 91) {{
                        cell.classList.add('start');
                    }} else if (noFlyZones.includes(positionCode)) {{
                        cell.classList.add('no-fly');
                    }} else if (patrolPath.includes(positionCode)) {{
                        if (positionCode === 17) {{
                            cell.classList.add('highlight-17');
                        }} else {{
                            cell.classList.add('patrol');
                        }}
                        // 添加路径序号
                        const pathIndex = patrolPath.indexOf(positionCode) + 1;
                        const pathNumber = document.createElement('div');
                        pathNumber.className = 'path-number';
                        pathNumber.textContent = pathIndex;
                        cell.appendChild(pathNumber);
                    }} else if (returnPath.includes(positionCode)) {{
                        cell.classList.add('return');
                        // 添加返航序号
                        const returnIndex = returnPath.indexOf(positionCode) + 1;
                        const pathNumber = document.createElement('div');
                        pathNumber.className = 'path-number';
                        pathNumber.textContent = 'R' + returnIndex;
                        cell.appendChild(pathNumber);
                    }} else {{
                        cell.classList.add('normal');
                    }}
                    
                    grid.appendChild(cell);
                }}
            }}
        }}
        
        // 页面加载时生成网格
        document.addEventListener('DOMContentLoaded', generateGrid);
    </script>
</body>
</html>'''
    
    # 保存HTML文件
    with open('path_050.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print("✅ 已更新 path_050.html")
    print(f"   包含位置17: 是 (第{patrol_path.index(17)+1}个点)")

if __name__ == "__main__":
    print("正在更新路径50的HTML文件...")
    update_path_50_html()
    print("HTML文件更新完成！")
