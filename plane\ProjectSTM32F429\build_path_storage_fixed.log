*** Using Compiler 'V5.06 update 7 (build 960)', folder: 'D:\keil5\ARM\ARMCC\Bin'
Build target 'Ano_LX'
compiling path_storage.c...
..\FcSrc\User\path_storage.c(15): error:  #5: cannot open source input file "ANO_DT.h": No such file or directory
  #include "ANO_DT.h"
..\FcSrc\User\path_storage.c: 0 warnings, 1 error
compiling zigbee.c...
..\FcSrc\User\zigbee.c(510): error:  #20: identifier "WORK_POINT_ARRAY_SIZE" is undefined
      for (int i = 0; i < WORK_POINT_ARRAY_SIZE; i++)
..\FcSrc\User\zigbee.c(528): warning:  #223-D: function "path_planner_position_code_to_index" declared implicitly
          int index = path_planner_position_code_to_index((int)position_code);
..\FcSrc\User\zigbee.c(530): error:  #20: identifier "WORK_POINT_ARRAY_SIZE" is undefined
          if (index >= 0 && index < WORK_POINT_ARRAY_SIZE)
..\FcSrc\User\zigbee.c(552): error:  #20: identifier "WORK_POINT_ARRAY_SIZE" is undefined
              for (int i = 0; i < WORK_POINT_ARRAY_SIZE; i++)
..\FcSrc\User\zigbee.c(606): error:  #20: identifier "ANOLOGCOLOR_YELLOW" is undefined
              AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_YELLOW,
..\FcSrc\User\zigbee.c(835): error:  #20: identifier "WORK_POINT_ARRAY_SIZE" is undefined
      for (int i = 0; i < WORK_POINT_ARRAY_SIZE; i++) {
..\FcSrc\User\zigbee.c(846): warning:  #223-D: function "path_planner_position_code_to_index" declared implicitly
          int index = path_planner_position_code_to_index((int)position_code);
..\FcSrc\User\zigbee.c(848): error:  #20: identifier "WORK_POINT_ARRAY_SIZE" is undefined
          if (index >= 0 && index < WORK_POINT_ARRAY_SIZE) {
..\FcSrc\User\zigbee.c(55): warning:  #550-D: variable "animal_records"  was set but never used
  static animal_record_t animal_records[MAX_ANIMAL_RECORDS];  // 动物发现记录数组
..\FcSrc\User\zigbee.c(62): warning:  #550-D: variable "coordinate_offset_x"  was set but never used
  static s16 coordinate_offset_x = 0;        // X轴偏移量
..\FcSrc\User\zigbee.c(63): warning:  #550-D: variable "coordinate_offset_y"  was set but never used
  static s16 coordinate_offset_y = 0;        // Y轴偏移量
..\FcSrc\User\zigbee.c: 5 warnings, 6 errors
compiling User_Task.c...
..\FcSrc\User_Task.c(1034): warning:  #177-D: variable "ch8_value"  was declared but never referenced
  	uint16_t ch8_value = rc_in.rc_ch.st_data.ch_[ch_8_aux4];
..\FcSrc\User_Task.c(1099): warning:  #223-D: function "path_planner_lite_nearest_neighbor" declared implicitly
      int result = path_planner_lite_nearest_neighbor(work_pos, WORK_POINT_ARRAY_SIZE, no_fly_zones, no_fly_count);
..\FcSrc\User_Task.c(1101): error:  #20: identifier "PATH_PLANNER_SUCCESS" is undefined
      if (result == PATH_PLANNER_SUCCESS) {
..\FcSrc\User_Task.c(1392): warning:  #177-D: variable "total_distance"  was declared but never referenced
      float total_distance = 0.0f;
..\FcSrc\User_Task.c(1693): warning:  #1-D: last line of file ends without a newline
  }
..\FcSrc\User_Task.c(1678): warning:  #177-D: function "calculate_descent_progress"  was declared but never referenced
  static float calculate_descent_progress(void)
..\FcSrc\User_Task.c(106): warning:  #177-D: variable "dadian_f"  was declared but never referenced
  static u8 dadian_f = 0;
..\FcSrc\User_Task.c(122): warning:  #550-D: variable "descent_angle"  was set but never used
  static float descent_angle = 45.0f;            // 降落角度
..\FcSrc\User_Task.c(276): warning:  #177-D: function "is_dadian_down_command"  was declared but never referenced
  static inline bool is_dadian_down_command(uint16_t ch_value) {
..\FcSrc\User_Task.c(300): warning:  #177-D: function "is_CAM_reached"  was declared but never referenced
  static inline bool is_CAM_reached(void) {
..\FcSrc\User_Task.c(659): warning:  #177-D: function "handle_return_home"  was declared but never referenced
  static void handle_return_home(void)
..\FcSrc\User_Task.c: 10 warnings, 1 error
".\build\ANO_LX.axf" - 8 Error(s), 15 Warning(s).
Target not created.
Build Time Elapsed:  00:00:02
