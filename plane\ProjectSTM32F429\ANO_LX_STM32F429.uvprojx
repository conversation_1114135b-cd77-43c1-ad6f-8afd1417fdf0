<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>Ano_LX</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>5060960::V5.06 update 7 (build 960)::.\ARMCC</pCCUsed>
      <uAC6>0</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>STM32F429VGTx</Device>
          <Vendor>STMicroelectronics</Vendor>
          <PackID>Keil.STM32F4xx_DFP.2.2.0</PackID>
          <PackURL>http://www.keil.com/pack</PackURL>
          <Cpu>IRAM(0x20000000,0x00030000) IRAM2(0x10000000,0x00010000) IROM(0x08000000,0x00100000) CPUTYPE("Cortex-M4") FPU2 CLOCK(12000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN1 -FF0STM32F4xx_1024 -********** -********* -FP0($$Device:STM32F429VGTx$CMSIS\Flash\STM32F4xx_1024.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:STM32F429VGTx$Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:STM32F429VGTx$CMSIS\SVD\STM32F429.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\build\</OutputDirectory>
          <OutputName>ANO_LX</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\build\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>fromelf.exe --bin -o ./ANO-LX.bin ./build/Ano_LX.axf</UserProg1Name>
            <UserProg2Name>./Keil5_disp_size_bar.exe ./ build 0 ■ _</UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> -REMAP -MPU</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> -MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <hadIRAM2>1</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>4</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x30000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x100000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x100000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x30000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x10000000</StartAddress>
                <Size>0x10000</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>1</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>0</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>1</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>2</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>1</v6Lang>
            <v6LangP>1</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls>--c99</MiscControls>
              <Define>USE_STDPERIPH_DRIVER,STM32F429_439xx,CONFIG_USB_DWC2_PORT=FS_PORT</Define>
              <Undefine></Undefine>
              <IncludePath>.\build;..\FcSrc;..\DriversBsp;..\DriversMcu\STM32F4xx\Drivers;..\DriversMcu\STM32F4xx\Libraries\STM32F4xx_StdPeriph_Driver\inc;..\DriversMcu\STM32F4xx\Libraries\CMSIS\Include;..\DriversMcu\STM32F4xx\Libraries\CMSIS\ST\STM32F4xx\Include;..\DriversMcu\STM32F4xx;..\FcSrc\AnoPTv8;..\DriversMcu\STM32F4xx\cherryusb;..\FcSrc\User</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>4</ClangAsOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x08000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>FcSrc</GroupName>
          <Files>
            <File>
              <FileName>SysConfig.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\FcSrc\SysConfig.h</FilePath>
            </File>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FcSrc\main.c</FilePath>
            </File>
            <File>
              <FileName>Ano_Scheduler.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FcSrc\Ano_Scheduler.c</FilePath>
            </File>
            <File>
              <FileName>Ano_Scheduler.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\FcSrc\Ano_Scheduler.h</FilePath>
            </File>
            <File>
              <FileName>User_Task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FcSrc\User_Task.c</FilePath>
            </File>
            <File>
              <FileName>User_Task.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\FcSrc\User_Task.h</FilePath>
            </File>
            <File>
              <FileName>DataTransfer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FcSrc\DataTransfer.c</FilePath>
            </File>
            <File>
              <FileName>DataTransfer.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\FcSrc\DataTransfer.h</FilePath>
            </File>
            <File>
              <FileName>LX_ExtSensor.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FcSrc\LX_ExtSensor.c</FilePath>
            </File>
            <File>
              <FileName>LX_ExtSensor.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\FcSrc\LX_ExtSensor.h</FilePath>
            </File>
            <File>
              <FileName>LX_FcFunc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FcSrc\LX_FcFunc.c</FilePath>
            </File>
            <File>
              <FileName>LX_FcFunc.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\FcSrc\LX_FcFunc.h</FilePath>
            </File>
            <File>
              <FileName>LX_FcState.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FcSrc\LX_FcState.c</FilePath>
            </File>
            <File>
              <FileName>LX_FcState.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\FcSrc\LX_FcState.h</FilePath>
            </File>
            <File>
              <FileName>LX_LowLevelFunc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FcSrc\LX_LowLevelFunc.c</FilePath>
            </File>
            <File>
              <FileName>LX_LowLevelFunc.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\FcSrc\LX_LowLevelFunc.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>User</GroupName>
          <Files>
            <File>
              <FileName>crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FcSrc\User\crc.c</FilePath>
            </File>
            <File>
              <FileName>g_port.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FcSrc\User\g_port.c</FilePath>
            </File>
            <File>
              <FileName>mid360.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FcSrc\User\mid360.c</FilePath>
            </File>
            <File>
              <FileName>PID.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FcSrc\User\PID.c</FilePath>
            </File>
            <File>
              <FileName>zigbee.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FcSrc\User\zigbee.c</FilePath>
            </File>
            <File>
              <FileName>Maixcam.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FcSrc\User\Maixcam.c</FilePath>
            </File>
            <File>
              <FileName>Tofsense-m.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FcSrc\User\Tofsense-m.c</FilePath>
            </File>
            <File>
              <FileName>tofmini.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FcSrc\User\tofmini.c</FilePath>
            </File>
            <File>
              <FileName>path_storage.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FcSrc\User\path_storage.c</FilePath>
            </File>
            <File>
              <FileName>path_storage.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\FcSrc\User\path_storage.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>AnoPTv8</GroupName>
          <Files>
            <File>
              <FileName>AnoPTv8.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\FcSrc\AnoPTv8\AnoPTv8.h</FilePath>
            </File>
            <File>
              <FileName>AnoPTv8ExAPI.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FcSrc\AnoPTv8\AnoPTv8ExAPI.c</FilePath>
            </File>
            <File>
              <FileName>AnoPTv8ExAPI.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\FcSrc\AnoPTv8\AnoPTv8ExAPI.h</FilePath>
            </File>
            <File>
              <FileName>AnoPTv8Run.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FcSrc\AnoPTv8\AnoPTv8Run.c</FilePath>
            </File>
            <File>
              <FileName>AnoPTv8Run.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\FcSrc\AnoPTv8\AnoPTv8Run.h</FilePath>
            </File>
            <File>
              <FileName>AnoPTv8Cmd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FcSrc\AnoPTv8\AnoPTv8Cmd.c</FilePath>
            </File>
            <File>
              <FileName>AnoPTv8Cmd.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\FcSrc\AnoPTv8\AnoPTv8Cmd.h</FilePath>
            </File>
            <File>
              <FileName>AnoPTv8Par.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FcSrc\AnoPTv8\AnoPTv8Par.c</FilePath>
            </File>
            <File>
              <FileName>AnoPTv8Par.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\FcSrc\AnoPTv8\AnoPTv8Par.h</FilePath>
            </File>
            <File>
              <FileName>AnoPTv8FrameFactory.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FcSrc\AnoPTv8\AnoPTv8FrameFactory.c</FilePath>
            </File>
            <File>
              <FileName>AnoPTv8FrameFactory.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\FcSrc\AnoPTv8\AnoPTv8FrameFactory.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>DriversBsp</GroupName>
          <Files>
            <File>
              <FileName>Drv_BSP.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversBsp\Drv_BSP.c</FilePath>
            </File>
            <File>
              <FileName>Drv_BSP.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\DriversBsp\Drv_BSP.h</FilePath>
            </File>
            <File>
              <FileName>Ano_Math.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversBsp\Ano_Math.c</FilePath>
            </File>
            <File>
              <FileName>Ano_Math.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\DriversBsp\Ano_Math.h</FilePath>
            </File>
            <File>
              <FileName>Drv_AnoOf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversBsp\Drv_AnoOf.c</FilePath>
            </File>
            <File>
              <FileName>Drv_AnoOf.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\DriversBsp\Drv_AnoOf.h</FilePath>
            </File>
            <File>
              <FileName>Drv_UbloxGPS.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversBsp\Drv_UbloxGPS.c</FilePath>
            </File>
            <File>
              <FileName>Drv_UbloxGPS.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\DriversBsp\Drv_UbloxGPS.h</FilePath>
            </File>
            <File>
              <FileName>DrvAnoOF_ptv7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversBsp\DrvAnoOF_ptv7.c</FilePath>
            </File>
            <File>
              <FileName>DrvAnoOF_ptv7.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\DriversBsp\DrvAnoOF_ptv7.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>DriversMcu</GroupName>
          <Files>
            <File>
              <FileName>McuConfig.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\DriversMcu\STM32F4xx\McuConfig.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_it.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversMcu\STM32F4xx\Drivers\stm32f4xx_it.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_conf.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\DriversMcu\STM32F4xx\Drivers\stm32f4xx_conf.h</FilePath>
            </File>
            <File>
              <FileName>Drv_Sys.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversMcu\STM32F4xx\Drivers\Drv_Sys.c</FilePath>
            </File>
            <File>
              <FileName>Drv_Sys.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\DriversMcu\STM32F4xx\Drivers\Drv_Sys.h</FilePath>
            </File>
            <File>
              <FileName>Drv_led.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversMcu\STM32F4xx\Drivers\Drv_led.c</FilePath>
            </File>
            <File>
              <FileName>Drv_led.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\DriversMcu\STM32F4xx\Drivers\Drv_led.h</FilePath>
            </File>
            <File>
              <FileName>Drv_Timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversMcu\STM32F4xx\Drivers\Drv_Timer.c</FilePath>
            </File>
            <File>
              <FileName>Drv_Timer.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\DriversMcu\STM32F4xx\Drivers\Drv_Timer.h</FilePath>
            </File>
            <File>
              <FileName>Drv_Uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversMcu\STM32F4xx\Drivers\Drv_Uart.c</FilePath>
            </File>
            <File>
              <FileName>Drv_Uart.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\DriversMcu\STM32F4xx\Drivers\Drv_Uart.h</FilePath>
            </File>
            <File>
              <FileName>Drv_PwmOut.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversMcu\STM32F4xx\Drivers\Drv_PwmOut.c</FilePath>
            </File>
            <File>
              <FileName>Drv_PwmOut.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\DriversMcu\STM32F4xx\Drivers\Drv_PwmOut.h</FilePath>
            </File>
            <File>
              <FileName>Drv_Dshot600.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversMcu\STM32F4xx\Drivers\Drv_Dshot600.c</FilePath>
            </File>
            <File>
              <FileName>Drv_Dshot600.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\DriversMcu\STM32F4xx\Drivers\Drv_Dshot600.h</FilePath>
            </File>
            <File>
              <FileName>Drv_RcIn.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversMcu\STM32F4xx\Drivers\Drv_RcIn.c</FilePath>
            </File>
            <File>
              <FileName>Drv_RcIn.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\DriversMcu\STM32F4xx\Drivers\Drv_RcIn.h</FilePath>
            </File>
            <File>
              <FileName>Drv_Usb.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversMcu\STM32F4xx\Drivers\Drv_Usb.c</FilePath>
            </File>
            <File>
              <FileName>Drv_Usb.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\DriversMcu\STM32F4xx\Drivers\Drv_Usb.h</FilePath>
            </File>
            <File>
              <FileName>Drv_Adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversMcu\STM32F4xx\Drivers\Drv_Adc.c</FilePath>
            </File>
            <File>
              <FileName>Drv_Adc.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\DriversMcu\STM32F4xx\Drivers\Drv_Adc.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>LibrariesMcu</GroupName>
          <Files>
            <File>
              <FileName>system_stm32f4xx.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversMcu\STM32F4xx\Libraries\CMSIS\ST\STM32F4xx\Source\Templates\system_stm32f4xx.c</FilePath>
            </File>
            <File>
              <FileName>misc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversMcu\STM32F4xx\Libraries\STM32F4xx_StdPeriph_Driver\src\misc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversMcu\STM32F4xx\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_adc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversMcu\STM32F4xx\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_dma.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_exti.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversMcu\STM32F4xx\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_exti.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversMcu\STM32F4xx\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_flash.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversMcu\STM32F4xx\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_gpio.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_rcc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversMcu\STM32F4xx\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_rcc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversMcu\STM32F4xx\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_spi.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_tim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversMcu\STM32F4xx\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_tim.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversMcu\STM32F4xx\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_usart.c</FilePath>
            </File>
            <File>
              <FileName>startup_stm32f429_439xx.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\DriversMcu\STM32F4xx\Libraries\CMSIS\ST\STM32F4xx\Source\Templates\arm\startup_stm32f429_439xx.s</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>LibrariesMcu/cherryusb</GroupName>
          <Files>
            <File>
              <FileName>usb_dc_dwc2.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversMcu\STM32F4xx\cherryusb\usb_dc_dwc2.c</FilePath>
            </File>
            <File>
              <FileName>usbd_cdc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversMcu\STM32F4xx\cherryusb\usbd_cdc.c</FilePath>
            </File>
            <File>
              <FileName>usbd_core.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\DriversMcu\STM32F4xx\cherryusb\usbd_core.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Doc</GroupName>
          <Files>
            <File>
              <FileName>note.txt</FileName>
              <FileType>5</FileType>
              <FilePath>..\Doc\note.txt</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components/>
    <files/>
  </RTE>

  <LayerInfo>
    <Layers>
      <Layer>
        <LayName>ANO_LX_STM32F429</LayName>
        <LayPrjMark>1</LayPrjMark>
      </Layer>
    </Layers>
  </LayerInfo>

</Project>
