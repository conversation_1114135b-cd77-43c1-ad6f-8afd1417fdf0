/*==========================================================================
 * 文件名称：reset_time_flag_improvement_example.c
 * 版权信息：米醋电子工作室
 * 创建日期：2025-01-24
 * 编码格式：UTF-8
 * 
 * 说明：reset_time_flag 变量改进示例代码
 * 本文件展示了三种不同的改进方案，从简单到复杂的渐进式改进
===========================================================================*/

#include "User_Task.h"
#include <stdbool.h>

// ================== 方案1: 立即改进 - 改善命名和注释 ==================

/**
 * @brief 降落定时器重置标志
 * @note 用于在降落过程中断时异步重置定时器
 * 
 * 工作原理：
 * 1. 当用户在降落过程中下拨开关中断降落时，handle_landing_command() 设置此标志为1
 * 2. execute_landing_sequence_v2() 检查此标志，如果为1则重置定时器并清零标志
 * 3. 这种设计避免了函数间直接参数传递，适合当前的调用架构
 * 
 * 使用位置：
 * - 设置：handle_landing_command() 函数，降落中断时
 * - 检查：execute_landing_sequence_v2() 函数，定时器管理时
 * 
 * 注意事项：
 * - 此标志仅在降落功能中使用，不要在其他功能中复用
 * - 标志设置后必须确保 execute_landing_sequence_v2() 被调用以清零标志
 */
static u8 landing_timer_reset_flag = 0;

// 改进后的函数示例
static void handle_landing_command_improved_v1(uint16_t ch_value)
{
    static bool switch_ever_pulled_down = false;
    static landing_state_t landing_state = LANDING_STATE_IDLE;
    static u16 landing_timer_ms = 0;

    // Zigbee控制状态检查
    if (zigbee_up_f == 1) {
        return;
    }

    // 权限管理：检测开关状态变化
    if (is_mission_command(ch_value)) {
        switch_ever_pulled_down = true;

        // 如果正在降落过程中，开关下拨则中断降落
        if (landing_state == LANDING_STATE_ACTIVE) {
            landing_state = LANDING_STATE_IDLE;
            landing_timer_reset_flag = 1;  // 【改进】更清晰的变量名
        }
        return;
    }

    // 其他降落逻辑...
}

static void execute_landing_sequence_improved_v1(u16 *timer_ms, landing_state_t *state)
{
    dadian_cnt = 0;
    target_pos[2] = LANDING_HEIGHT;
    Z_flag_Control(1);

    // 【改进】更清晰的注释说明
    if (landing_timer_reset_flag == 1) {
        *timer_ms = 0;                    // 重置降落定时器
        landing_timer_reset_flag = 0;     // 清除重置标志
    } else if (*timer_ms < RC_LANDING_TIMEOUT_MS) {
        BEEP_flag = 1;
        *timer_ms += RC_TASK_INTERVAL_MS;
    } else {
        // 降落超时处理
        *state = LANDING_STATE_TIMEOUT;
        all_flag_reset();
        *timer_ms = 0;
        Z_flag_Control(0);
        FC_Lock();
        BEEP_flag = 0;
    }
}

// ================== 方案2: 中期重构 - 直接参数传递 ==================

/**
 * @brief 降落状态管理结构体
 * @note 封装降落相关的所有状态信息，便于统一管理
 */
typedef struct {
    bool switch_ever_pulled_down;    // 开关是否曾下拨过
    landing_state_t state;           // 当前降落状态
    u16 timer_ms;                    // 降落定时器
} landing_context_t;

// 全局降落上下文（替代分散的静态变量）
static landing_context_t g_landing_context = {
    .switch_ever_pulled_down = false,
    .state = LANDING_STATE_IDLE,
    .timer_ms = 0
};

static void handle_landing_command_improved_v2(uint16_t ch_value)
{
    // Zigbee控制状态检查
    if (zigbee_up_f == 1) {
        return;
    }

    // 权限管理：检测开关状态变化
    if (is_mission_command(ch_value)) {
        g_landing_context.switch_ever_pulled_down = true;

        // 如果正在降落过程中，开关下拨则中断降落
        if (g_landing_context.state == LANDING_STATE_ACTIVE) {
            g_landing_context.state = LANDING_STATE_IDLE;
            g_landing_context.timer_ms = 0;  // 【改进】直接重置，无需标志
        }
        return;
    }

    // 降落命令检查
    if (is_landing_command(ch_value)) {
        if (!g_landing_context.switch_ever_pulled_down) {
            return;  // 无权限
        }

        // 开始降落
        if (g_landing_context.state == LANDING_STATE_IDLE) {
            g_landing_context.state = LANDING_STATE_ACTIVE;
            g_landing_context.timer_ms = 0;  // 重置定时器
        }

        // 执行降落序列
        if (g_landing_context.state == LANDING_STATE_ACTIVE) {
            execute_landing_sequence_improved_v2();
        }
    }
}

static void execute_landing_sequence_improved_v2(void)
{
    dadian_cnt = 0;
    target_pos[2] = LANDING_HEIGHT;
    Z_flag_Control(1);

    // 【改进】直接操作全局上下文，逻辑更清晰
    if (g_landing_context.timer_ms < RC_LANDING_TIMEOUT_MS) {
        BEEP_flag = 1;
        g_landing_context.timer_ms += RC_TASK_INTERVAL_MS;
    } else {
        // 降落超时处理
        g_landing_context.state = LANDING_STATE_TIMEOUT;
        g_landing_context.timer_ms = 0;
        all_flag_reset();
        Z_flag_Control(0);
        FC_Lock();
        BEEP_flag = 0;
    }
}

// ================== 方案3: 长期优化 - 统一定时器管理 ==================

/**
 * @brief 通用定时器管理结构体
 * @note 提供统一的定时器管理接口，支持多种定时器操作
 */
typedef struct {
    u16 current_ms;          // 当前计时值
    u16 timeout_ms;          // 超时阈值
    u16 interval_ms;         // 更新间隔
    bool is_active;          // 定时器是否激活
    bool is_expired;         // 是否已超时
} managed_timer_t;

/**
 * @brief 降落管理器结构体
 * @note 封装所有降落相关的状态和定时器
 */
typedef struct {
    bool switch_ever_pulled_down;
    landing_state_t state;
    managed_timer_t timer;
} landing_manager_t;

// 全局降落管理器
static landing_manager_t g_landing_manager = {
    .switch_ever_pulled_down = false,
    .state = LANDING_STATE_IDLE,
    .timer = {
        .current_ms = 0,
        .timeout_ms = RC_LANDING_TIMEOUT_MS,
        .interval_ms = RC_TASK_INTERVAL_MS,
        .is_active = false,
        .is_expired = false
    }
};

// 定时器管理接口
static void timer_start(managed_timer_t* timer, u16 timeout_ms)
{
    timer->current_ms = 0;
    timer->timeout_ms = timeout_ms;
    timer->is_active = true;
    timer->is_expired = false;
}

static void timer_reset(managed_timer_t* timer)
{
    timer->current_ms = 0;
    timer->is_expired = false;
}

static void timer_stop(managed_timer_t* timer)
{
    timer->is_active = false;
    timer->current_ms = 0;
    timer->is_expired = false;
}

static void timer_update(managed_timer_t* timer)
{
    if (!timer->is_active) {
        return;
    }

    timer->current_ms += timer->interval_ms;
    
    if (timer->current_ms >= timer->timeout_ms) {
        timer->is_expired = true;
        timer->is_active = false;
    }
}

static bool timer_is_expired(const managed_timer_t* timer)
{
    return timer->is_expired;
}

static void handle_landing_command_improved_v3(uint16_t ch_value)
{
    // Zigbee控制状态检查
    if (zigbee_up_f == 1) {
        return;
    }

    // 权限管理：检测开关状态变化
    if (is_mission_command(ch_value)) {
        g_landing_manager.switch_ever_pulled_down = true;

        // 如果正在降落过程中，开关下拨则中断降落
        if (g_landing_manager.state == LANDING_STATE_ACTIVE) {
            g_landing_manager.state = LANDING_STATE_IDLE;
            timer_stop(&g_landing_manager.timer);  // 【改进】使用统一接口
        }
        return;
    }

    // 降落命令检查
    if (is_landing_command(ch_value)) {
        if (!g_landing_manager.switch_ever_pulled_down) {
            return;
        }

        // 开始降落
        if (g_landing_manager.state == LANDING_STATE_IDLE) {
            g_landing_manager.state = LANDING_STATE_ACTIVE;
            timer_start(&g_landing_manager.timer, RC_LANDING_TIMEOUT_MS);
        }

        // 执行降落序列
        if (g_landing_manager.state == LANDING_STATE_ACTIVE) {
            execute_landing_sequence_improved_v3();
        }
    }
}

static void execute_landing_sequence_improved_v3(void)
{
    dadian_cnt = 0;
    target_pos[2] = LANDING_HEIGHT;
    Z_flag_Control(1);

    // 【改进】使用统一的定时器管理接口
    timer_update(&g_landing_manager.timer);

    if (!timer_is_expired(&g_landing_manager.timer)) {
        BEEP_flag = 1;  // 降落过程中蜂鸣器提示
    } else {
        // 降落超时处理
        g_landing_manager.state = LANDING_STATE_TIMEOUT;
        timer_stop(&g_landing_manager.timer);
        all_flag_reset();
        Z_flag_Control(0);
        FC_Lock();
        BEEP_flag = 0;
    }
}

// ================== 使用示例和测试代码 ==================

/**
 * @brief 测试函数：验证改进方案的正确性
 * @note 此函数仅用于演示，实际项目中应使用专门的测试框架
 */
static void test_landing_improvements(void)
{
    // 测试方案1：基本功能验证
    landing_timer_reset_flag = 1;
    u16 test_timer = 100;
    landing_state_t test_state = LANDING_STATE_ACTIVE;
    
    execute_landing_sequence_improved_v1(&test_timer, &test_state);
    
    // 验证：定时器应该被重置，标志应该被清零
    // assert(test_timer == 0);
    // assert(landing_timer_reset_flag == 0);

    // 测试方案2：上下文管理验证
    g_landing_context.state = LANDING_STATE_ACTIVE;
    g_landing_context.timer_ms = 100;
    
    handle_landing_command_improved_v2(1800);  // 模拟开关下拨
    
    // 验证：状态应该变为IDLE，定时器应该被重置
    // assert(g_landing_context.state == LANDING_STATE_IDLE);
    // assert(g_landing_context.timer_ms == 0);

    // 测试方案3：定时器管理验证
    timer_start(&g_landing_manager.timer, 1000);
    timer_update(&g_landing_manager.timer);
    
    // 验证：定时器应该正常工作
    // assert(g_landing_manager.timer.is_active == true);
    // assert(g_landing_manager.timer.current_ms == RC_TASK_INTERVAL_MS);
}

/*
 * 总结：
 * 
 * 方案1 - 立即改进：
 * - 优点：实施简单，风险低，显著提升可读性
 * - 缺点：仍然存在全局状态依赖
 * - 适用：当前版本的快速改进
 * 
 * 方案2 - 中期重构：
 * - 优点：消除标志位，逻辑更清晰，便于调试
 * - 缺点：需要修改函数接口，影响调用方
 * - 适用：下一个版本的重构目标
 * 
 * 方案3 - 长期优化：
 * - 优点：统一管理，易于扩展，架构清晰
 * - 缺点：实施复杂，需要大量测试
 * - 适用：系统架构升级时的目标方案
 */
