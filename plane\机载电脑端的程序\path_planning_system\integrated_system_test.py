#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整系统集成与测试
版权信息：米醋电子工作室
创建日期：2025-07-31
作者：Mike (团队领袖) 统筹全体团队
编码格式：UTF-8

功能描述：
将所有模块集成为完整的路径规划系统，进行端到端测试，
生成最终的预计算路径查找表和HTML可视化展示。验证系统的整体功能和性能。
"""

import os
import json
import time
import subprocess
from typing import Dict, List, Tuple
from pathlib import Path

class IntegratedSystemTester:
    """完整系统集成测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.test_results = {}
        self.performance_metrics = {}
        self.start_time = time.time()
        
    def test_module_availability(self) -> Dict:
        """测试所有模块的可用性"""
        print("🔍 测试模块可用性...")
        
        required_files = [
            'no_fly_zone_combinations.json',
            'dijkstra_precomputed_paths.json', 
            'optimized_return_paths.json',
            'visualization_index.html',
            'comprehensive_validation_report.json',
            '../../FcSrc/User/path_storage.h',
            '../../FcSrc/User/path_storage.c'
        ]
        
        module_status = {}
        
        for file_path in required_files:
            exists = os.path.exists(file_path)
            module_status[file_path] = {
                'exists': exists,
                'size': os.path.getsize(file_path) if exists else 0
            }
            
            status = "✅" if exists else "❌"
            size_info = f"({module_status[file_path]['size']} bytes)" if exists else ""
            print(f"   {status} {file_path} {size_info}")
        
        success_count = sum(1 for status in module_status.values() if status['exists'])
        success_rate = (success_count / len(required_files)) * 100
        
        return {
            'module_status': module_status,
            'success_count': success_count,
            'total_count': len(required_files),
            'success_rate': success_rate
        }
    
    def test_data_integrity(self) -> Dict:
        """测试数据完整性"""
        print("\n🔍 测试数据完整性...")
        
        data_tests = {}
        
        # 测试禁飞区组合数据
        try:
            with open('no_fly_zone_combinations.json', 'r', encoding='utf-8') as f:
                no_fly_data = json.load(f)
            
            combinations = no_fly_data['combinations']
            data_tests['no_fly_combinations'] = {
                'loaded': True,
                'count': len(combinations),
                'expected': 92,
                'valid': len(combinations) == 92
            }
            print(f"   ✅ 禁飞区组合: {len(combinations)}/92")
            
        except Exception as e:
            data_tests['no_fly_combinations'] = {'loaded': False, 'error': str(e)}
            print(f"   ❌ 禁飞区组合加载失败: {e}")
        
        # 测试路径预计算数据
        try:
            with open('optimized_return_paths.json', 'r', encoding='utf-8') as f:
                path_data = json.load(f)
            
            paths = path_data['path_data']
            data_tests['precomputed_paths'] = {
                'loaded': True,
                'count': len(paths),
                'expected': 92,
                'valid': len(paths) == 92
            }
            print(f"   ✅ 预计算路径: {len(paths)}/92")
            
        except Exception as e:
            data_tests['precomputed_paths'] = {'loaded': False, 'error': str(e)}
            print(f"   ❌ 预计算路径加载失败: {e}")
        
        # 测试验证报告数据
        try:
            with open('comprehensive_validation_report.json', 'r', encoding='utf-8') as f:
                validation_data = json.load(f)
            
            success_rate = validation_data['summary']['success_rate']
            data_tests['validation_report'] = {
                'loaded': True,
                'success_rate': success_rate,
                'valid': success_rate >= 95
            }
            print(f"   ✅ 验证报告: {success_rate}%成功率")
            
        except Exception as e:
            data_tests['validation_report'] = {'loaded': False, 'error': str(e)}
            print(f"   ❌ 验证报告加载失败: {e}")
        
        # 计算总体数据完整性
        valid_tests = sum(1 for test in data_tests.values() 
                         if test.get('loaded', False) and test.get('valid', False))
        total_tests = len(data_tests)
        integrity_rate = (valid_tests / total_tests) * 100
        
        return {
            'data_tests': data_tests,
            'valid_tests': valid_tests,
            'total_tests': total_tests,
            'integrity_rate': integrity_rate
        }
    
    def test_html_visualization(self) -> Dict:
        """测试HTML可视化"""
        print("\n🔍 测试HTML可视化...")
        
        html_tests = {}
        
        # 检查索引页面
        index_file = 'visualization_index.html'
        if os.path.exists(index_file):
            with open(index_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            html_tests['index_page'] = {
                'exists': True,
                'size': len(content),
                'has_css': 'style>' in content,
                'has_grid': 'path-grid' in content,
                'has_stats': 'stat-item' in content
            }
            print(f"   ✅ 索引页面: {len(content)} 字符")
        else:
            html_tests['index_page'] = {'exists': False}
            print(f"   ❌ 索引页面不存在")
        
        # 检查详细页面
        detail_pages = []
        for i in range(1, 93):
            page_file = f'path_{i:03d}.html'
            if os.path.exists(page_file):
                detail_pages.append(page_file)
        
        html_tests['detail_pages'] = {
            'count': len(detail_pages),
            'expected': 92,
            'complete': len(detail_pages) == 92
        }
        print(f"   ✅ 详细页面: {len(detail_pages)}/92")
        
        # 检查测试页面
        test_file = 'test_visualization.html'
        if os.path.exists(test_file):
            html_tests['test_page'] = {'exists': True}
            print(f"   ✅ 测试页面存在")
        else:
            html_tests['test_page'] = {'exists': False}
            print(f"   ⚠️ 测试页面不存在")
        
        return html_tests
    
    def test_c_code_generation(self) -> Dict:
        """测试C代码生成"""
        print("\n🔍 测试C代码生成...")
        
        c_tests = {}
        
        # 检查头文件
        header_file = '../../FcSrc/User/path_storage.h'
        if os.path.exists(header_file):
            with open(header_file, 'r', encoding='utf-8') as f:
                header_content = f.read()
            
            c_tests['header_file'] = {
                'exists': True,
                'size': len(header_content),
                'has_struct': 'precomputed_path_t' in header_content,
                'has_functions': 'find_precomputed_path' in header_content,
                'has_macros': 'MAX_PATH_LENGTH' in header_content
            }
            print(f"   ✅ 头文件: {len(header_content)} 字符")
        else:
            c_tests['header_file'] = {'exists': False}
            print(f"   ❌ 头文件不存在")
        
        # 检查源文件
        source_file = '../../FcSrc/User/path_storage.c'
        if os.path.exists(source_file):
            with open(source_file, 'r', encoding='utf-8') as f:
                source_content = f.read()
            
            c_tests['source_file'] = {
                'exists': True,
                'size': len(source_content),
                'has_data': 'path_lookup_table' in source_content,
                'has_functions': 'find_precomputed_path' in source_content,
                'has_debug': 'AnoPTv8SendStr' in source_content
            }
            print(f"   ✅ 源文件: {len(source_content)} 字符")
        else:
            c_tests['source_file'] = {'exists': False}
            print(f"   ❌ 源文件不存在")
        
        return c_tests
    
    def test_system_performance(self) -> Dict:
        """测试系统性能"""
        print("\n🔍 测试系统性能...")
        
        performance = {}
        
        # 测试数据加载性能
        start_time = time.time()
        try:
            with open('optimized_return_paths.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            load_time = time.time() - start_time
            performance['data_loading'] = {
                'time_seconds': load_time,
                'data_size_mb': os.path.getsize('optimized_return_paths.json') / 1024 / 1024,
                'performance': 'excellent' if load_time < 1 else 'good' if load_time < 3 else 'poor'
            }
            print(f"   ✅ 数据加载: {load_time:.3f}秒")
        except Exception as e:
            performance['data_loading'] = {'error': str(e)}
            print(f"   ❌ 数据加载失败: {e}")
        
        # 测试内存使用
        total_size = 0
        file_sizes = {}
        
        key_files = [
            'no_fly_zone_combinations.json',
            'optimized_return_paths.json',
            'comprehensive_validation_report.json'
        ]
        
        for file_path in key_files:
            if os.path.exists(file_path):
                size = os.path.getsize(file_path)
                file_sizes[file_path] = size
                total_size += size
        
        performance['memory_usage'] = {
            'total_size_mb': total_size / 1024 / 1024,
            'file_sizes': file_sizes,
            'efficiency': 'excellent' if total_size < 10*1024*1024 else 'good'
        }
        print(f"   ✅ 内存使用: {total_size/1024/1024:.2f}MB")
        
        return performance
    
    def generate_final_report(self) -> Dict:
        """生成最终集成测试报告"""
        print("\n📋 生成最终集成测试报告...")
        
        total_time = time.time() - self.start_time
        
        # 汇总所有测试结果
        final_report = {
            'metadata': {
                'test_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                'total_duration_seconds': total_time,
                'tester': 'Mike (团队领袖) 统筹全体团队'
            },
            'test_results': self.test_results,
            'performance_metrics': self.performance_metrics,
            'summary': {
                'modules_available': self.test_results.get('modules', {}).get('success_rate', 0),
                'data_integrity': self.test_results.get('data', {}).get('integrity_rate', 0),
                'html_complete': self.test_results.get('html', {}).get('detail_pages', {}).get('complete', False),
                'c_code_ready': all([
                    self.test_results.get('c_code', {}).get('header_file', {}).get('exists', False),
                    self.test_results.get('c_code', {}).get('source_file', {}).get('exists', False)
                ])
            }
        }
        
        # 计算总体成功率
        success_indicators = [
            final_report['summary']['modules_available'] >= 95,
            final_report['summary']['data_integrity'] >= 95,
            final_report['summary']['html_complete'],
            final_report['summary']['c_code_ready']
        ]
        
        overall_success_rate = (sum(success_indicators) / len(success_indicators)) * 100
        final_report['summary']['overall_success_rate'] = overall_success_rate
        
        # 保存报告
        with open('integrated_system_test_report.json', 'w', encoding='utf-8') as f:
            json.dump(final_report, f, indent=2, ensure_ascii=False)
        
        return final_report
    
    def run_complete_test(self) -> Dict:
        """运行完整的系统集成测试"""
        print("🚀 开始完整系统集成测试")
        print("=" * 70)
        
        # 1. 测试模块可用性
        self.test_results['modules'] = self.test_module_availability()
        
        # 2. 测试数据完整性
        self.test_results['data'] = self.test_data_integrity()
        
        # 3. 测试HTML可视化
        self.test_results['html'] = self.test_html_visualization()
        
        # 4. 测试C代码生成
        self.test_results['c_code'] = self.test_c_code_generation()
        
        # 5. 测试系统性能
        self.performance_metrics = self.test_system_performance()
        
        # 6. 生成最终报告
        final_report = self.generate_final_report()
        
        return final_report

def main():
    """主函数"""
    print("🚀 完整系统集成与测试")
    print("=" * 70)
    print("版权信息：米醋电子工作室")
    print("创建日期：2025-07-31")
    print("作者：Mike (团队领袖) 统筹全体团队")
    print("=" * 70)
    print()
    
    # 创建测试器并运行完整测试
    tester = IntegratedSystemTester()
    final_report = tester.run_complete_test()
    
    # 输出测试结果
    print(f"\n🎉 系统集成测试完成!")
    print(f"✅ 模块可用性: {final_report['summary']['modules_available']:.1f}%")
    print(f"✅ 数据完整性: {final_report['summary']['data_integrity']:.1f}%")
    print(f"✅ HTML可视化: {'完整' if final_report['summary']['html_complete'] else '不完整'}")
    print(f"✅ C代码生成: {'就绪' if final_report['summary']['c_code_ready'] else '未就绪'}")
    print(f"✅ 总体成功率: {final_report['summary']['overall_success_rate']:.1f}%")
    print(f"✅ 测试耗时: {final_report['metadata']['total_duration_seconds']:.2f}秒")
    
    print(f"\n📁 详细报告已保存: integrated_system_test_report.json")
    
    # 最终评价
    success_rate = final_report['summary']['overall_success_rate']
    if success_rate >= 95:
        print("\n🏆 系统集成测试：优秀")
        print("✅ 所有模块协同工作正常")
        print("✅ 数据完整性和质量优秀")
        print("✅ 可视化和代码生成完整")
        print("✅ 系统已准备就绪，可投入使用")
    elif success_rate >= 80:
        print("\n⚠️ 系统集成测试：良好")
        print("部分功能需要关注，但整体可用")
    else:
        print("\n❌ 系统集成测试：需要改进")
        print("存在重要问题，需要修复后使用")
    
    print("=" * 70)
    
    return final_report

if __name__ == "__main__":
    main()
