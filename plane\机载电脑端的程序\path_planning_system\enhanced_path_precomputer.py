#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版路径预计算器 - 支持时间优化的Dijkstra算法
版权信息：米醋电子工作室
创建日期：2025-07-31
作者：Bob (架构师)
编码格式：UTF-8

功能描述：
基于现有DijkstraPlanner，增强支持巡查和返航的时间差异优化。
巡查阶段每点停留时间长，返航阶段移动时间短，优化总飞行时间。
"""

import json
import time
import heapq
import math
from typing import List, Tuple, Dict, Set
from collections import defaultdict

# 简化导入，避免numpy依赖
class GridMap:
    """简化的网格地图类"""
    GRID_ROWS = 7
    GRID_COLS = 9
    
    def __init__(self):
        self.no_fly_zones = set()
    
    def set_no_fly_zones(self, zones: List[int]):
        self.no_fly_zones = set(zones)
    
    def is_valid_position(self, row: int, col: int) -> bool:
        if row < 0 or row >= self.GRID_ROWS or col < 0 or col >= self.GRID_COLS:
            return False
        position_code = (col + 1) * 10 + (row + 1)
        return position_code not in self.no_fly_zones
    
    def position_code_to_grid(self, position_code: int) -> Tuple[int, int]:
        col_num = position_code // 10
        row_num = position_code % 10
        row = row_num - 1
        col = col_num - 1
        return (row, col)
    
    def grid_to_position_code(self, row: int, col: int) -> int:
        return (col + 1) * 10 + (row + 1)

class EnhancedDijkstraPlanner:
    """增强版Dijkstra路径规划器 - 支持时间优化"""
    
    def __init__(self, patrol_time_per_point=10.0, travel_time_per_unit=1.0):
        """
        初始化规划器
        
        参数:
            patrol_time_per_point: 巡查阶段每点停留时间
            travel_time_per_unit: 移动单位时间（直线移动=1，对角线移动=√2）
        """
        self.patrol_time_per_point = patrol_time_per_point
        self.travel_time_per_unit = travel_time_per_unit
        
    def calculate_move_cost(self, from_pos: Tuple[int, int], to_pos: Tuple[int, int], 
                          is_patrol_phase: bool = True) -> float:
        """
        计算移动代价（考虑巡查和返航的时间差异）
        
        参数:
            from_pos: 起始位置
            to_pos: 目标位置
            is_patrol_phase: 是否为巡查阶段
            
        返回:
            float: 移动代价（时间）
        """
        # 计算移动距离
        dx = abs(to_pos[0] - from_pos[0])
        dy = abs(to_pos[1] - from_pos[1])
        
        # 移动时间
        if dx == 1 and dy == 1:
            move_time = math.sqrt(2) * self.travel_time_per_unit  # 对角线移动
        elif dx + dy == 1:
            move_time = 1.0 * self.travel_time_per_unit  # 直线移动
        else:
            move_time = math.sqrt(dx*dx + dy*dy) * self.travel_time_per_unit  # 其他情况
        
        # 巡查阶段需要加上停留时间
        if is_patrol_phase:
            return move_time + self.patrol_time_per_point
        else:
            return move_time
    
    def check_diagonal_safety(self, from_pos: Tuple[int, int], to_pos: Tuple[int, int], 
                            grid_map: GridMap) -> bool:
        """
        检查对角线移动的安全性（避免穿越禁飞区角点）
        
        参数:
            from_pos: 起始位置
            to_pos: 目标位置
            grid_map: 网格地图
            
        返回:
            bool: 是否安全
        """
        dx = to_pos[0] - from_pos[0]
        dy = to_pos[1] - from_pos[1]
        
        # 只检查对角线移动
        if abs(dx) == 1 and abs(dy) == 1:
            # 检查对角线路径上的两个相邻点
            corner1 = (from_pos[0] + dx, from_pos[1])
            corner2 = (from_pos[0], from_pos[1] + dy)
            
            # 如果任一角点是禁飞区，则对角线移动不安全
            if not grid_map.is_valid_position(corner1[0], corner1[1]) or \
               not grid_map.is_valid_position(corner2[0], corner2[1]):
                return False
        
        return True
    
    def get_valid_neighbors(self, pos: Tuple[int, int], grid_map: GridMap) -> List[Tuple[int, int]]:
        """
        获取有效的相邻位置（8方向，考虑安全约束）
        
        参数:
            pos: 当前位置
            grid_map: 网格地图
            
        返回:
            List[Tuple[int, int]]: 有效相邻位置列表
        """
        neighbors = []
        directions = [(-1, -1), (-1, 0), (-1, 1), (0, -1), 
                     (0, 1), (1, -1), (1, 0), (1, 1)]
        
        for dr, dc in directions:
            new_pos = (pos[0] + dr, pos[1] + dc)
            
            # 检查位置有效性
            if grid_map.is_valid_position(new_pos[0], new_pos[1]):
                # 检查对角线移动安全性
                if self.check_diagonal_safety(pos, new_pos, grid_map):
                    neighbors.append(new_pos)
        
        return neighbors
    
    def plan_patrol_path(self, grid_map: GridMap, start: Tuple[int, int]) -> Dict:
        """
        规划巡查路径（遍历所有非禁飞区点）
        
        参数:
            grid_map: 网格地图
            start: 起始位置
            
        返回:
            Dict: 路径规划结果
        """
        start_time = time.time()
        
        # 收集所有可访问的点
        accessible_points = []
        for row in range(GridMap.GRID_ROWS):
            for col in range(GridMap.GRID_COLS):
                if grid_map.is_valid_position(row, col):
                    accessible_points.append((row, col))
        
        if not accessible_points or start not in accessible_points:
            return {
                'path_sequence': [],
                'path_length': 0,
                'total_time': 0,
                'total_distance': 0,
                'computation_time_ms': 0,
                'coverage_rate': 0,
                'is_valid': False
            }
        
        # 使用改进的最近邻算法进行全点遍历
        path = [start]
        visited = {start}
        current_pos = start
        total_time = 0
        total_distance = 0
        
        while len(visited) < len(accessible_points):
            # 找到最近的未访问点（考虑时间代价）
            min_cost = float('inf')
            next_pos = None
            
            for point in accessible_points:
                if point not in visited:
                    # 使用Dijkstra找到最短路径
                    path_cost, path_distance = self._dijkstra_shortest_path(
                        current_pos, point, grid_map, is_patrol_phase=True)
                    
                    if path_cost < min_cost:
                        min_cost = path_cost
                        next_pos = point
            
            if next_pos is None:
                break
            
            # 添加到路径
            path.append(next_pos)
            visited.add(next_pos)
            
            # 更新统计信息
            move_cost = self.calculate_move_cost(current_pos, next_pos, is_patrol_phase=True)
            total_time += move_cost
            total_distance += math.sqrt((next_pos[0] - current_pos[0])**2 + 
                                      (next_pos[1] - current_pos[1])**2)
            
            current_pos = next_pos
        
        # 转换为position_code序列
        path_sequence = [grid_map.grid_to_position_code(pos[0], pos[1]) for pos in path]
        
        computation_time = (time.time() - start_time) * 1000
        coverage_rate = (len(path) / len(accessible_points)) * 100
        
        return {
            'path_sequence': path_sequence,
            'path_length': len(path),
            'total_time': total_time,
            'total_distance': total_distance,
            'computation_time_ms': computation_time,
            'coverage_rate': coverage_rate,
            'is_valid': len(path) == len(accessible_points)
        }
    
    def _dijkstra_shortest_path(self, start: Tuple[int, int], goal: Tuple[int, int], 
                               grid_map: GridMap, is_patrol_phase: bool = True) -> Tuple[float, float]:
        """
        使用Dijkstra算法计算两点间最短路径
        
        参数:
            start: 起始位置
            goal: 目标位置
            grid_map: 网格地图
            is_patrol_phase: 是否为巡查阶段
            
        返回:
            Tuple[float, float]: (时间代价, 距离)
        """
        if start == goal:
            return (0, 0)
        
        # 初始化
        distances = defaultdict(lambda: float('inf'))
        distances[start] = 0
        pq = [(0, start)]
        visited = set()
        
        while pq:
            current_dist, current_pos = heapq.heappop(pq)
            
            if current_pos in visited:
                continue
                
            visited.add(current_pos)
            
            if current_pos == goal:
                return (current_dist, current_dist)  # 简化：时间=距离
            
            # 检查所有邻居
            for neighbor in self.get_valid_neighbors(current_pos, grid_map):
                if neighbor not in visited:
                    cost = self.calculate_move_cost(current_pos, neighbor, is_patrol_phase)
                    new_dist = current_dist + cost
                    
                    if new_dist < distances[neighbor]:
                        distances[neighbor] = new_dist
                        heapq.heappush(pq, (new_dist, neighbor))
        
        return (float('inf'), float('inf'))  # 无法到达

class EnhancedPathPrecomputer:
    """增强版路径预计算器"""
    
    def __init__(self, patrol_time_per_point=10.0, travel_time_per_unit=1.0):
        """
        初始化预计算器
        
        参数:
            patrol_time_per_point: 巡查阶段每点停留时间
            travel_time_per_unit: 移动单位时间
        """
        self.planner = EnhancedDijkstraPlanner(patrol_time_per_point, travel_time_per_unit)
        self.start_position_code = 91  # A9B1
        self.start_grid_pos = (0, 8)   # A9B1对应的网格坐标
        
    def compute_path_for_combination(self, no_fly_zones: List[int]) -> Dict:
        """
        为指定禁飞区组合计算最优路径
        
        参数:
            no_fly_zones: 禁飞区position_code列表
            
        返回:
            Dict: 路径计算结果
        """
        # 创建网格地图并设置禁飞区
        grid_map = GridMap()
        grid_map.set_no_fly_zones(no_fly_zones)
        
        # 执行巡查路径规划
        patrol_result = self.planner.plan_patrol_path(grid_map, self.start_grid_pos)
        
        # 计算返航路径（如果巡查成功）
        return_path = []
        return_time = 0
        return_distance = 0
        
        if patrol_result['is_valid'] and patrol_result['path_length'] > 0:
            # 获取巡查路径的最后一个点
            last_position_code = patrol_result['path_sequence'][-1]
            last_grid_pos = grid_map.position_code_to_grid(last_position_code)
            
            # 计算返航路径（不停留，只考虑移动时间）
            return_result = self._calculate_return_path(grid_map, last_grid_pos, self.start_grid_pos)
            return_path = return_result['path_sequence']
            return_time = return_result['total_time']
            return_distance = return_result['total_distance']
        
        # 计算总时间（巡查时间 + 返航时间）
        total_flight_time = patrol_result['total_time'] + return_time
        
        # 计算覆盖率
        expected_points = 63 - len(no_fly_zones)
        coverage_rate = (patrol_result['path_length'] / expected_points * 100) if expected_points > 0 else 0
        
        return {
            'no_fly_zones': [int(x) for x in no_fly_zones],
            'patrol_path_sequence': patrol_result['path_sequence'],
            'patrol_path_length': patrol_result['path_length'],
            'patrol_time': patrol_result['total_time'],
            'patrol_distance': patrol_result['total_distance'],
            'return_path_sequence': return_path,
            'return_path_length': len(return_path),
            'return_time': return_time,
            'return_distance': return_distance,
            'total_flight_time': total_flight_time,
            'computation_time_ms': patrol_result['computation_time_ms'],
            'coverage_rate': coverage_rate,
            'is_valid': patrol_result['is_valid']
        }
    
    def _calculate_return_path(self, grid_map: GridMap, start: Tuple[int, int], 
                              goal: Tuple[int, int]) -> Dict:
        """
        计算返航路径（优化移动时间，不停留）
        
        参数:
            grid_map: 网格地图
            start: 起始位置
            goal: 目标位置
            
        返回:
            Dict: 返航路径结果
        """
        if start == goal:
            return {
                'path_sequence': [grid_map.grid_to_position_code(start[0], start[1])],
                'total_time': 0,
                'total_distance': 0
            }
        
        # 使用A*算法计算最短返航路径
        path = self._astar_path(grid_map, start, goal)
        
        if not path:
            return {
                'path_sequence': [],
                'total_time': float('inf'),
                'total_distance': float('inf')
            }
        
        # 计算返航时间和距离
        total_time = 0
        total_distance = 0
        
        for i in range(len(path) - 1):
            current_pos = path[i]
            next_pos = path[i + 1]
            
            # 返航阶段不停留，只计算移动时间
            move_time = self.planner.calculate_move_cost(current_pos, next_pos, is_patrol_phase=False)
            move_distance = math.sqrt((next_pos[0] - current_pos[0])**2 + 
                                    (next_pos[1] - current_pos[1])**2)
            
            total_time += move_time
            total_distance += move_distance
        
        # 转换为position_code序列
        path_sequence = [grid_map.grid_to_position_code(pos[0], pos[1]) for pos in path]
        
        return {
            'path_sequence': path_sequence,
            'total_time': total_time,
            'total_distance': total_distance
        }
    
    def _astar_path(self, grid_map: GridMap, start: Tuple[int, int], goal: Tuple[int, int]) -> List[Tuple[int, int]]:
        """
        A*算法计算最短路径
        
        参数:
            grid_map: 网格地图
            start: 起始位置
            goal: 目标位置
            
        返回:
            List[Tuple[int, int]]: 路径序列
        """
        def heuristic(pos1, pos2):
            return math.sqrt((pos1[0] - pos2[0])**2 + (pos1[1] - pos2[1])**2)
        
        open_set = [(0, start)]
        came_from = {}
        g_score = defaultdict(lambda: float('inf'))
        g_score[start] = 0
        f_score = defaultdict(lambda: float('inf'))
        f_score[start] = heuristic(start, goal)
        
        while open_set:
            current = heapq.heappop(open_set)[1]
            
            if current == goal:
                # 重建路径
                path = []
                while current in came_from:
                    path.append(current)
                    current = came_from[current]
                path.append(start)
                return path[::-1]
            
            for neighbor in self.planner.get_valid_neighbors(current, grid_map):
                tentative_g_score = g_score[current] + self.planner.calculate_move_cost(
                    current, neighbor, is_patrol_phase=False)
                
                if tentative_g_score < g_score[neighbor]:
                    came_from[neighbor] = current
                    g_score[neighbor] = tentative_g_score
                    f_score[neighbor] = g_score[neighbor] + heuristic(neighbor, goal)
                    heapq.heappush(open_set, (f_score[neighbor], neighbor))
        
        return []  # 无法到达

def main():
    """主函数 - 执行路径预计算"""
    print("🚀 Dijkstra算法路径预计算")
    print("=" * 70)
    print("版权信息：米醋电子工作室")
    print("创建日期：2025-07-31")
    print("作者：Bob (架构师)")
    print("=" * 70)
    print()
    
    # 加载禁飞区组合数据
    try:
        with open('no_fly_zone_combinations.json', 'r', encoding='utf-8') as f:
            combinations_data = json.load(f)
        combinations = combinations_data['combinations']
        print(f"📁 成功加载 {len(combinations)} 个禁飞区组合")
    except FileNotFoundError:
        print("❌ 未找到禁飞区组合数据文件，请先运行禁飞区组合生成")
        return
    
    # 初始化预计算器（巡查停留10秒，移动1秒/单位）
    precomputer = EnhancedPathPrecomputer(patrol_time_per_point=10.0, travel_time_per_unit=1.0)
    
    print(f"🔄 开始预计算 {len(combinations)} 种禁飞区组合的最优路径...")
    print("⚙️  参数设置：巡查停留时间=10秒/点，移动时间=1秒/单位")
    print()
    
    results = []
    start_time = time.time()
    valid_count = 0
    
    for i, combination in enumerate(combinations):
        print(f"   计算进度: {i+1}/{len(combinations)} - 禁飞区: {combination}")
        
        try:
            result = precomputer.compute_path_for_combination(combination)
            results.append(result)
            
            if result['is_valid']:
                valid_count += 1
                print(f"     ✅ 巡查: {result['patrol_path_length']}点, "
                      f"返航: {result['return_path_length']}点, "
                      f"总时间: {result['total_flight_time']:.1f}秒, "
                      f"覆盖率: {result['coverage_rate']:.1f}%, "
                      f"计算时间: {result['computation_time_ms']:.2f}ms")
            else:
                print(f"     ❌ 路径计算失败")
                
        except Exception as e:
            print(f"     ❌ 计算异常: {e}")
            results.append({
                'no_fly_zones': combination,
                'is_valid': False,
                'error': str(e)
            })
    
    total_time = time.time() - start_time
    print()
    print(f"🎉 预计算完成! 总耗时: {total_time:.2f}秒")
    print(f"📊 成功率: {valid_count}/{len(combinations)} ({valid_count/len(combinations)*100:.1f}%)")
    
    # 保存结果
    output_data = {
        'metadata': {
            'generation_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'total_combinations': len(combinations),
            'valid_paths': valid_count,
            'computation_time_seconds': total_time,
            'parameters': {
                'patrol_time_per_point': 10.0,
                'travel_time_per_unit': 1.0
            }
        },
        'path_data': results
    }
    
    output_file = "dijkstra_precomputed_paths.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, indent=2, ensure_ascii=False)
    
    print(f"📁 结果已保存到: {output_file}")
    print("=" * 70)

if __name__ == "__main__":
    main()
