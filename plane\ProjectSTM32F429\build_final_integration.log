*** Using Compiler 'V5.06 update 7 (build 960)', folder: 'D:\keil5\ARM\ARMCC\Bin'
Build target 'Ano_LX'
compiling User_Task.c...
..\FcSrc\User_Task.c(1036): warning:  #177-D: variable "ch8_value"  was declared but never referenced
  	uint16_t ch8_value = rc_in.rc_ch.st_data.ch_[ch_8_aux4];
..\FcSrc\User_Task.c(1394): warning:  #177-D: variable "total_distance"  was declared but never referenced
      float total_distance = 0.0f;
..\FcSrc\User_Task.c(1732): error:  #169: expected a declaration
      return progress;
..\FcSrc\User_Task.c(1733): warning:  #1-D: last line of file ends without a newline
  }
..\FcSrc\User_Task.c(1733): error:  #169: expected a declaration
  }
..\FcSrc\User_Task.c(1680): warning:  #177-D: function "calculate_descent_progress"  was declared but never referenced
  static float calculate_descent_progress(void)
..\FcSrc\User_Task.c(108): warning:  #177-D: variable "dadian_f"  was declared but never referenced
  static u8 dadian_f = 0;
..\FcSrc\User_Task.c(124): warning:  #550-D: variable "descent_angle"  was set but never used
  static float descent_angle = 45.0f;            // 降落角度
..\FcSrc\User_Task.c(278): warning:  #177-D: function "is_dadian_down_command"  was declared but never referenced
  static inline bool is_dadian_down_command(uint16_t ch_value) {
..\FcSrc\User_Task.c(302): warning:  #177-D: function "is_CAM_reached"  was declared but never referenced
  static inline bool is_CAM_reached(void) {
..\FcSrc\User_Task.c(661): warning:  #177-D: function "handle_return_home"  was declared but never referenced
  static void handle_return_home(void)
..\FcSrc\User_Task.c: 9 warnings, 2 errors
compiling path_storage.c...
..\FcSrc\User\path_storage.c(1547): error:  #20: identifier "ANOLOGCOLOR_YELLOW" is undefined
      AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_YELLOW, 
..\FcSrc\User\path_storage.c: 0 warnings, 1 error
compiling zigbee.c...
..\FcSrc\User\zigbee.c(61): warning:  #550-D: variable "animal_records"  was set but never used
  static animal_record_t animal_records[MAX_ANIMAL_RECORDS];  // 动物发现记录数组
..\FcSrc\User\zigbee.c(68): warning:  #550-D: variable "coordinate_offset_x"  was set but never used
  static s16 coordinate_offset_x = 0;        // X轴偏移量
..\FcSrc\User\zigbee.c(69): warning:  #550-D: variable "coordinate_offset_y"  was set but never used
  static s16 coordinate_offset_y = 0;        // Y轴偏移量
..\FcSrc\User\zigbee.c: 3 warnings, 0 errors
".\build\ANO_LX.axf" - 3 Error(s), 12 Warning(s).
Target not created.
Build Time Elapsed:  00:00:02
