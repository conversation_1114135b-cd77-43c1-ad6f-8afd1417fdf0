*** Using Compiler 'V5.06 update 7 (build 960)', folder: 'D:\keil5\ARM\ARMCC\Bin'
Build target 'Ano_LX'
compiling zigbee.c...
..\FcSrc\User\zigbee.c(593): error:  #20: identifier "ANOLOGCOLOR_BLUE" is undefined
              AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_BLUE, original_info);
..\FcSrc\User\zigbee.c(62): warning:  #550-D: variable "animal_records"  was set but never used
  static animal_record_t animal_records[MAX_ANIMAL_RECORDS];  // 动物发现记录数组
..\FcSrc\User\zigbee.c(69): warning:  #550-D: variable "coordinate_offset_x"  was set but never used
  static s16 coordinate_offset_x = 0;        // X轴偏移量
..\FcSrc\User\zigbee.c(70): warning:  #550-D: variable "coordinate_offset_y"  was set but never used
  static s16 coordinate_offset_y = 0;        // Y轴偏移量
..\FcSrc\User\zigbee.c: 3 warnings, 1 error
compiling path_storage.c...
".\build\ANO_LX.axf" - 1 Error(s), 3 Warning(s).
Target not created.
Build Time Elapsed:  00:00:02
