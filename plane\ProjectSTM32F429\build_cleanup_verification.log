*** Using Compiler 'V5.06 update 7 (build 960)', folder: 'D:\keil5\ARM\ARMCC\Bin'
Build target 'Ano_LX'
compiling User_Task.c...
..\FcSrc\User_Task.c(1022): warning:  #177-D: variable "ch8_value"  was declared but never referenced
  	uint16_t ch8_value = rc_in.rc_ch.st_data.ch_[ch_8_aux4];
..\FcSrc\User_Task.c(1327): warning:  #177-D: variable "total_distance"  was declared but never referenced
      float total_distance = 0.0f;
..\FcSrc\User_Task.c(1613): warning:  #177-D: function "calculate_descent_progress"  was declared but never referenced
  static float calculate_descent_progress(void)
..\FcSrc\User_Task.c(107): warning:  #177-D: variable "dadian_f"  was declared but never referenced
  static u8 dadian_f = 0;
..\FcSrc\User_Task.c(123): warning:  #550-D: variable "descent_angle"  was set but never used
  static float descent_angle = 45.0f;            // 降落角度
..\FcSrc\User_Task.c(277): warning:  #177-D: function "is_dadian_down_command"  was declared but never referenced
  static inline bool is_dadian_down_command(uint16_t ch_value) {
..\FcSrc\User_Task.c(301): warning:  #177-D: function "is_CAM_reached"  was declared but never referenced
  static inline bool is_CAM_reached(void) {
..\FcSrc\User_Task.c(660): warning:  #177-D: function "handle_return_home"  was declared but never referenced
  static void handle_return_home(void)
..\FcSrc\User_Task.c: 8 warnings, 0 errors
linking...
Program Size: Code=93884 RO-data=9984 RW-data=3392 ZI-data=21464  
FromELF: creating hex file...
After Build - User command #1: fromelf.exe --bin -o ./ANO-LX.bin ./build/Ano_LX.axf
".\build\ANO_LX.axf" - 0 Error(s), 8 Warning(s).
Build Time Elapsed:  00:00:02
