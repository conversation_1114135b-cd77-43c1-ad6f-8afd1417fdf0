#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
C代码验证工具
版权信息：米醋电子工作室
创建日期：2025-07-31
作者：Alex (Engineer)
编码格式：UTF-8

功能描述：
验证生成的C代码的语法正确性、数据完整性和功能合理性
"""

import re
import os
from typing import Dict, List, Tuple

class CCodeValidator:
    """C代码验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.errors = []
        self.warnings = []
        self.stats = {}
    
    def validate_header_file(self, header_path: str) -> Dict:
        """验证头文件"""
        print(f"🔍 验证头文件: {header_path}")
        
        if not os.path.exists(header_path):
            self.errors.append(f"头文件不存在: {header_path}")
            return {'valid': False, 'errors': self.errors}
        
        with open(header_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查基本结构
        checks = [
            (r'#ifndef\s+\w+', "缺少头文件保护宏"),
            (r'#define\s+\w+', "缺少宏定义"),
            (r'#endif', "缺少#endif"),
            (r'typedef\s+struct', "缺少结构体定义"),
            (r'precomputed_path_t', "缺少主要数据结构"),
            (r'find_precomputed_path', "缺少主要函数声明"),
            (r'MAX_PATH_LENGTH\s+60', "路径长度宏定义错误"),
            (r'PRECOMPUTED_PATH_COUNT\s+92', "路径数量宏定义错误")
        ]
        
        for pattern, error_msg in checks:
            if not re.search(pattern, content):
                self.errors.append(f"头文件: {error_msg}")
        
        # 统计信息
        self.stats['header_lines'] = len(content.split('\n'))
        self.stats['header_size'] = len(content)
        
        print(f"   头文件行数: {self.stats['header_lines']}")
        print(f"   头文件大小: {self.stats['header_size']} 字节")
        
        return {'valid': len(self.errors) == 0, 'errors': self.errors}
    
    def validate_source_file(self, source_path: str) -> Dict:
        """验证源文件"""
        print(f"\n🔍 验证源文件: {source_path}")
        
        if not os.path.exists(source_path):
            self.errors.append(f"源文件不存在: {source_path}")
            return {'valid': False, 'errors': self.errors}
        
        with open(source_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查基本结构
        checks = [
            (r'#include\s+"path_storage\.h"', "缺少头文件包含"),
            (r'static\s+const\s+precomputed_path_t\s+path_lookup_table', "缺少查找表定义"),
            (r'int\s+find_precomputed_path', "缺少主要函数实现"),
            (r'int\s+find_precomputed_return_path', "缺少返航路径函数"),
            (r'int\s+validate_no_fly_zones', "缺少验证函数"),
            (r'AnoPTv8SendStr', "缺少调试输出"),
            (r'return\s+-1', "缺少错误处理"),
            (r'return\s+\w+', "缺少正常返回")
        ]
        
        for pattern, error_msg in checks:
            if not re.search(pattern, content):
                self.errors.append(f"源文件: {error_msg}")
        
        # 检查数据完整性
        self.validate_data_integrity(content)
        
        # 统计信息
        self.stats['source_lines'] = len(content.split('\n'))
        self.stats['source_size'] = len(content)
        
        print(f"   源文件行数: {self.stats['source_lines']}")
        print(f"   源文件大小: {self.stats['source_size']} 字节")
        
        return {'valid': len(self.errors) == 0, 'errors': self.errors}
    
    def validate_data_integrity(self, content: str):
        """验证数据完整性"""
        print("   🔍 验证数据完整性...")
        
        # 查找路径数据条目
        path_entries = re.findall(r'\{[^}]+\},[^}]+\{[^}]+\},[^}]+\{[^}]+\}', content)
        
        if len(path_entries) == 0:
            self.errors.append("未找到路径数据条目")
            return
        
        print(f"   找到路径数据条目: {len(path_entries)} 个")
        
        # 验证第一个条目的结构
        first_entry = path_entries[0] if path_entries else ""
        
        # 检查禁飞区数据
        no_fly_pattern = r'\{(\d+),\s*(\d+),\s*(\d+)\}'
        no_fly_match = re.search(no_fly_pattern, first_entry)
        if no_fly_match:
            zones = [int(x) for x in no_fly_match.groups()]
            print(f"   第一个条目禁飞区: {zones}")
            
            # 验证position_code范围
            for zone in zones:
                if zone < 11 or zone > 97:
                    self.errors.append(f"禁飞区position_code超出范围: {zone}")
                
                col = zone // 10
                row = zone % 10
                if col < 1 or col > 9 or row < 1 or row > 7:
                    self.errors.append(f"禁飞区坐标无效: {zone} -> ({col},{row})")
        else:
            self.errors.append("第一个条目禁飞区格式错误")
        
        # 检查路径长度
        length_pattern = r'(\d+),\s*//\s*巡查路径长度'
        length_match = re.search(length_pattern, first_entry)
        if length_match:
            path_length = int(length_match.group(1))
            print(f"   第一个条目路径长度: {path_length}")
            
            if path_length != 60:
                self.errors.append(f"路径长度错误: {path_length}, 期望: 60")
        else:
            self.warnings.append("无法解析路径长度")
        
        # 检查返航路径长度
        return_length_pattern = r'(\d+),\s*//\s*返航路径长度'
        return_length_match = re.search(return_length_pattern, first_entry)
        if return_length_match:
            return_length = int(return_length_match.group(1))
            print(f"   第一个条目返航长度: {return_length}")
            
            if return_length < 1 or return_length > 25:
                self.warnings.append(f"返航路径长度异常: {return_length}")
        else:
            self.warnings.append("无法解析返航路径长度")
    
    def validate_function_signatures(self, content: str):
        """验证函数签名"""
        print("   🔍 验证函数签名...")
        
        expected_functions = [
            r'int\s+find_precomputed_path\s*\(\s*const\s+u8\*\s+no_fly_zones\s*,\s*u8\*\s+output_path\s*\)',
            r'int\s+find_precomputed_return_path\s*\(\s*const\s+u8\*\s+no_fly_zones\s*,\s*u8\*\s+output_path\s*\)',
            r'const\s+u8\*\s+find_precomputed_return_path_direct\s*\(',
            r'int\s+get_path_statistics\s*\(',
            r'int\s+validate_no_fly_zones\s*\(\s*const\s+u8\*\s+no_fly_zones\s*\)'
        ]
        
        for i, pattern in enumerate(expected_functions):
            if re.search(pattern, content):
                print(f"   ✅ 函数 {i+1} 签名正确")
            else:
                self.errors.append(f"函数 {i+1} 签名错误或缺失")
    
    def validate_memory_usage(self):
        """验证内存使用"""
        print("\n🔍 验证内存使用...")
        
        # 计算数据结构大小
        path_entry_size = 3 + 1 + 60 + 1 + 25  # 禁飞区3 + 长度1 + 路径60 + 返航长度1 + 返航路径25
        total_entries = 92
        total_size = path_entry_size * total_entries
        
        print(f"   单个路径条目大小: {path_entry_size} 字节")
        print(f"   总条目数: {total_entries}")
        print(f"   预计总大小: {total_size} 字节 ({total_size/1024:.1f} KB)")
        
        self.stats['estimated_memory'] = total_size
        
        # 检查是否超出单片机内存限制
        if total_size > 32 * 1024:  # 32KB限制
            self.warnings.append(f"数据大小可能超出单片机Flash限制: {total_size/1024:.1f}KB")
        else:
            print(f"   ✅ 内存使用合理: {total_size/1024:.1f}KB < 32KB")
    
    def generate_report(self) -> str:
        """生成验证报告"""
        report = []
        report.append("# C代码验证报告")
        report.append("=" * 50)
        report.append("")
        
        # 统计信息
        report.append("## 文件统计")
        report.append(f"- 头文件行数: {self.stats.get('header_lines', 0)}")
        report.append(f"- 源文件行数: {self.stats.get('source_lines', 0)}")
        report.append(f"- 头文件大小: {self.stats.get('header_size', 0)} 字节")
        report.append(f"- 源文件大小: {self.stats.get('source_size', 0)} 字节")
        report.append(f"- 预计内存使用: {self.stats.get('estimated_memory', 0)} 字节")
        report.append("")
        
        # 错误信息
        if self.errors:
            report.append("## ❌ 错误")
            for error in self.errors:
                report.append(f"- {error}")
            report.append("")
        
        # 警告信息
        if self.warnings:
            report.append("## ⚠️ 警告")
            for warning in self.warnings:
                report.append(f"- {warning}")
            report.append("")
        
        # 验证结果
        if not self.errors:
            report.append("## ✅ 验证结果")
            report.append("所有检查通过，C代码生成成功！")
        else:
            report.append("## ❌ 验证结果")
            report.append(f"发现 {len(self.errors)} 个错误，需要修复。")
        
        return "\n".join(report)

def main():
    """主函数"""
    print("🚀 C代码验证工具")
    print("=" * 70)
    print("版权信息：米醋电子工作室")
    print("创建日期：2025-07-31")
    print("作者：Alex (Engineer)")
    print("=" * 70)
    
    validator = CCodeValidator()
    
    # 验证文件路径
    header_path = "../../FcSrc/User/path_storage.h"
    source_path = "../../FcSrc/User/path_storage.c"
    
    # 执行验证
    header_result = validator.validate_header_file(header_path)
    source_result = validator.validate_source_file(source_path)
    
    # 验证函数签名
    if os.path.exists(source_path):
        with open(source_path, 'r', encoding='utf-8') as f:
            content = f.read()
        validator.validate_function_signatures(content)
    
    # 验证内存使用
    validator.validate_memory_usage()
    
    # 生成报告
    report = validator.generate_report()
    print("\n" + report)
    
    # 保存报告
    with open("c_code_validation_report.txt", "w", encoding="utf-8") as f:
        f.write(report)
    
    print(f"\n📁 验证报告已保存: c_code_validation_report.txt")
    
    # 总结
    if not validator.errors:
        print("\n🎉 C代码验证完成!")
        print("✅ 头文件结构正确")
        print("✅ 源文件实现完整")
        print("✅ 数据格式正确")
        print("✅ 函数签名正确")
        print("✅ 内存使用合理")
        print("✅ 可以直接用于单片机编译")
    else:
        print(f"\n❌ 发现 {len(validator.errors)} 个错误")
        print("需要修复后才能使用")

if __name__ == "__main__":
    main()
