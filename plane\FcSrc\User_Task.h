#ifndef __USER_TASK_H
#define __USER_TASK_H

#include "SysConfig.h"
#include <stdbool.h>




// ================== 状态枚举定义 ==================
/**
 * @brief 降落状态枚举（重构新增）
 * @note 遵循项目mid360_status_t的命名和结构风格，使用下划线命名规范
 *
 * 状态转换说明：
 * IDLE → ACTIVE：检测到降落命令且有权限时开始降落
 * ACTIVE → IDLE：降落过程中开关下拨或手动停止
 * ACTIVE → TIMEOUT：降落超时（4秒）后自动转换
 * TIMEOUT：超时状态，执行FC_Lock()锁定飞控
 * COMPLETED：降落完成状态（预留，当前版本未使用）
 */
typedef enum {
    LANDING_STATE_IDLE = 0,        // 降落空闲状态：等待降落命令
    LANDING_STATE_ACTIVE,          // 降落进行中：正在执行降落序列
    LANDING_STATE_TIMEOUT,         // 降落超时状态：4秒超时后执行FC_Lock()
    LANDING_STATE_COMPLETED        // 降落完成状态：预留状态（未使用）
} landing_state_t;

/**
 * @brief 降落上下文管理结构体
 * @note 封装降落功能相关的所有状态信息，替代分散的静态变量和全局标志
 *
 * 设计目标：
 * 1. 消除全局标志位依赖，提高代码可读性和可维护性
 * 2. 将相关状态集中管理，便于调试和状态追踪
 * 3. 支持直接参数传递，减少函数间的隐式耦合
 *
 * 使用方式：
 * - 在 handle_landing_command() 中直接操作上下文状态
 * - 在 execute_landing_sequence_v2() 中通过参数传递接收上下文
 * - 替代原有的 landing_timer_reset_flag 全局标志位机制
 */
typedef struct {
    bool switch_ever_pulled_down;    // 开关是否曾下拨过（权限管理）
    landing_state_t state;           // 当前降落状态
    u16 timer_ms;                    // 降落定时器（毫秒）
} landing_context_t;

/**
 * @brief 巡查状态枚举（重构新增）
 * @note 遵循项目landing_state_t的命名和结构风格，使用下划线命名规范
 *
 * 状态转换说明：
 * INIT → NAVIGATE：初始化完成，开始导航到目标点
 * NAVIGATE → ARRIVED：到达目标位置，开始稳定等待
 * ARRIVED → DETECT：位置稳定，开始动物检测阶段
 * DETECT → NEXT_POINT：检测完成，准备获取下一个巡查点
 * NEXT_POINT → INIT：获取下一个点成功，重新开始循环
 * NEXT_POINT → COMPLETED：所有巡查点完成，任务结束
 */
typedef enum {
    PATROL_STATE_INIT = 0,         // 巡查初始化状态：准备开始巡查
    PATROL_STATE_NAVIGATE,         // 巡查导航状态：正在导航到目标点
    PATROL_STATE_ARRIVED,          // 巡查到达状态：到达目标点，稳定等待
    PATROL_STATE_DETECT,           // 巡查检测状态：执行动物检测任务
    PATROL_STATE_NEXT_POINT,       // 巡查下一点状态：获取下一个巡查点
    PATROL_STATE_COMPLETED         // 巡查完成状态：所有巡查点已完成
} patrol_state_t;

/**
 * @brief 巡查上下文管理结构体
 * @note 封装巡查功能相关的所有状态信息，遵循landing_context_t的设计模式
 *
 * 设计目标：
 * 1. 替代硬编码的case 3-62逻辑，实现动态路径长度支持
 * 2. 将巡查相关状态集中管理，便于调试和状态追踪
 * 3. 支持路径重复检测和循环处理，提高系统健壮性
 *
 * 使用方式：
 * - 在 execute_mission_state_machine() 的case 3中初始化上下文
 * - 在 handle_patrol_state_machine() 中通过参数传递管理状态
 * - 替代原有的硬编码case序列，实现灵活的状态转换
 */
typedef struct {
    patrol_state_t state;            // 当前巡查状态
    int current_step;                // 当前巡查步骤索引（0-based）
    u16 timer_ms;                    // 巡查状态定时器（毫秒）
    bool detection_completed;        // 动物检测完成标志
} patrol_context_t;

// 外部变量声明
extern s16  home_pos[4];           // 起始位置坐标
extern s16  work_pos[63][6];       // 工作点坐标数组 (7×9网格) - 移除order字段
extern u8   BEEP_flag;             // 蜂鸣器标志
extern u8   mission_enabled_flag; // 任务执行标志（重构后的语义化命名）
extern u8   zigbee_up_f;           // Zigbee任务执行标志
extern u8   LED_f;                 // LED控制标志

// 函数声明
void UserTask_OneKeyCmd(void);

// 导航相关函数声明（供Path_Navigator使用）
void handle_work_point_navigation(u8 work_point_index);
bool is_position_reached(void);
bool is_yaw_reached(void);
bool handle_wait(u16 *timer_ms, u16 time);
bool land(u16 *timer_ms, u16 time);
extern u8 mission_step;
extern inline bool is_z_position_reached(void);

// 预计算路径相关函数声明
int get_next_patrol_point(void);

// 巡查状态机相关函数声明
static void handle_patrol_state_machine(patrol_context_t *context);
static void reset_patrol_context(patrol_context_t *context);
static void log_patrol_state_transition(patrol_state_t from, patrol_state_t to, int step);
static bool verify_patrol_compatibility(void);

/**
 * @brief 加载巡检路径引用（指针模式）
 * @param path_sequence Flash中路径序列的const指针
 * @param path_length 路径长度
 * @return 加载成功返回true，失败返回false
 * @note 性能优化版本，使用指针引用替代数组复制，节省60字节RAM
 */
bool load_patrol_reference(const u8* path_sequence, int path_length);

// 路径规划相关变量声明
extern int current_patrol_index;       // 当前巡查索引
extern u32 mission_start_time_ms;      // 任务开始时间
extern  void LED_PWM_Control(void);
extern void jiguang(u8 a,u8 b,u8 c);
#endif
