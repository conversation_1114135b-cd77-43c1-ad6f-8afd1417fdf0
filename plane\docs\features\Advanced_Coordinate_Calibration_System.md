# 高级坐标系校准系统实现文档

## 📋 系统概述

本文档描述了一个完整的坐标系校准解决方案，用于解决MID360激光雷达与实际坐标系之间的系统性偏差问题。该系统提供了多种校准方法，从简单的单点校准到精确的两点校准法。

## 🎯 解决的核心问题

### 问题分析
1. **MID360系统性偏差**: 显示(300,300)实际位置(297,295)
2. **整体坐标系偏移**: 起始位置漂移导致所有工作点偏移
3. **缩放误差**: 距离测量存在比例误差

### 坐标系架构理解
```
work_pos[50][4]        →  理想工作点坐标模板（相对于理想原点）
         ↓
home_pos[4]            →  实际起始位置（补偿整体漂移）
         ↓
actual_work_pos[50][4] →  最终工作坐标 = work_pos + home_pos
```

**关键决策**: 在`work_pos`层面进行校准，影响所有工作点的基础坐标模板。

## 🔧 校准方法详解

### 方法1: 数据备份与恢复 (ID=1,2)

#### 备份原始数据 (CMD=0x03, ID=1)
```bash
# 发送命令
AA FF 03 01 EA
```
- **功能**: 将当前`work_pos`数据备份到`work_pos_backup`数组
- **用途**: 校准前保护原始数据，支持恢复操作
- **时机**: 任何校准操作前都应先备份

#### 恢复原始数据 (CMD=0x03, ID=2)
```bash
# 发送命令
AA FF 03 02 EA
```
- **功能**: 从`work_pos_backup`恢复原始数据
- **用途**: 校准失败或需要重新开始时使用
- **效果**: 重置所有校准参数为默认值

### 方法2: 单点校准法 (ID=3)

#### 终点坐标校准 (CMD=0x03, ID=3)
```bash
# 将飞机手动搬到终点位置
# 发送命令
AA FF 03 03 EA
```
- **原理**: 直接用当前MID360坐标更新终点工作点
- **适用**: 快速校准终点位置
- **限制**: 只校准单个点，不解决系统性偏差

### 方法3: 两点校准法 (ID=4,5) - **推荐方案**

#### 第一步: 设置校准点1 (CMD=0x03, ID=4)
```bash
# 将飞机放在已知的第一个校准点（通常是原点位置）
AA FF 03 04 EA
```
- **功能**: 记录第一个校准点的MID360坐标
- **假设**: 第一个校准点对应实际坐标系原点(0,0)
- **存储**: `calib_point1_mid360_x/y`, `calib_point1_actual_x/y`

#### 第二步: 执行两点校准 (CMD=0x03, ID=5)
```bash
# 将飞机放在已知的第二个校准点（通常是终点位置）
AA FF 03 05 EA
```
- **功能**: 计算缩放和偏移参数，应用到所有工作点
- **算法**: 
  ```c
  // 计算缩放系数
  scale_x = actual_distance_x / mid360_distance_x
  scale_y = actual_distance_y / mid360_distance_y
  
  // 计算偏移量
  offset_x = actual_origin_x - (mid360_origin_x * scale_x)
  offset_y = actual_origin_y - (mid360_origin_y * scale_y)
  
  // 应用到所有工作点
  work_pos[i][0] = work_pos_backup[i][0] * scale_x + offset_x
  work_pos[i][1] = work_pos_backup[i][1] * scale_y + offset_y
  ```

## 📊 校准参数说明

### 核心参数
```c
static float coordinate_scale_x = 1.0f;    // X轴缩放系数
static float coordinate_scale_y = 1.0f;    // Y轴缩放系数
static s16 coordinate_offset_x = 0;        // X轴偏移量
static s16 coordinate_offset_y = 0;        // Y轴偏移量
```

### 校准点数据
```c
static s16 calib_point1_mid360_x/y;        // 第一校准点MID360坐标
static s16 calib_point1_actual_x/y;        // 第一校准点实际坐标
static bool calib_point1_set;              // 第一校准点设置标志
```

## 🚀 使用流程

### 完整校准流程（推荐）

#### 1. 准备阶段
```bash
# 备份原始数据
AA FF 03 01 EA
# 等待回复: AA FF 01 01 EA (成功)
```

#### 2. 第一校准点设置
```bash
# 将飞机手动搬到原点位置（或已知第一个校准点）
# 发送命令
AA FF 03 04 EA
# 等待回复: AA FF 04 04 EA (成功)
```

#### 3. 第二校准点校准
```bash
# 将飞机手动搬到终点位置（或已知第二个校准点）
# 发送命令
AA FF 03 05 EA
# 等待回复: AA FF 05 05 EA (成功)
```

#### 4. 验证校准结果
- 观察调试输出中的缩放系数和偏移量
- 检查关键工作点的校准后坐标
- 如果不满意，可以恢复原始数据重新校准

### 快速校准流程（仅终点）

```bash
# 将飞机搬到终点位置
AA FF 03 03 EA
# 等待回复: AA FF 03 03 EA (成功)
```

## 🔍 技术细节

### 坐标变换公式
```c
// 正向变换（从理想坐标到校准后坐标）
calibrated_x = ideal_x * scale_x + offset_x
calibrated_y = ideal_y * scale_y + offset_y

// 反向变换（从MID360坐标到实际坐标）
actual_x = mid360_x * scale_x + offset_x
actual_y = mid360_y * scale_y + offset_y
```

### 数据流向
```
原始work_pos → work_pos_backup (备份)
                     ↓
校准参数计算 ← 两个校准点的MID360坐标
                     ↓
work_pos_backup → 应用校准参数 → 新的work_pos
                     ↓
新work_pos + home_pos → actual_work_pos (任务执行)
```

### 误差分析
- **缩放误差**: 通过两点距离比较计算，精度取决于校准点距离
- **偏移误差**: 通过第一校准点位置计算，精度取决于原点定位
- **建议**: 校准点距离越远，缩放精度越高

## ⚠️ 注意事项

### 安全考虑
1. **备份重要**: 校准前必须备份原始数据
2. **验证结果**: 校准后检查关键点坐标合理性
3. **恢复机制**: 校准失败时可快速恢复原始数据

### 精度要求
1. **校准点选择**: 选择距离较远的两个已知点
2. **MID360状态**: 确保数据有效（link_sta=1, work_sta=1）
3. **环境稳定**: 校准时避免外界干扰

### 兼容性
1. **现有系统**: 与home_pos漂移补偿机制完全兼容
2. **任务执行**: 不影响现有任务状态机逻辑
3. **调试功能**: 保留所有原有调试接口

## 📈 预期效果

### 校准前
```
MID360显示: (300, 300) → 实际位置: (297, 295)
误差: X轴-3cm, Y轴-5cm
```

### 校准后
```
缩放系数: scale_x=0.99, scale_y=0.983
偏移量: offset_x=-3, offset_y=-5
校准精度: ±1cm (取决于校准点精度)
```

### 系统优势
1. **全局校准**: 一次校准影响所有工作点
2. **精度可控**: 通过校准点选择控制精度
3. **操作简单**: 通过Zigbee命令远程操作
4. **安全可靠**: 完整的备份恢复机制

## 📈 预期效果

### 校准前
```
MID360显示: (300, 300) → 实际位置: (297, 295)
误差: X轴-3cm, Y轴-5cm
```

### 校准后
```
缩放系数: scale_x=0.99, scale_y=0.983
偏移量: offset_x=-3, offset_y=-5
校准精度: ±1cm (取决于校准点精度)
```

### 系统优势
1. **全局校准**: 一次校准影响所有工作点
2. **精度可控**: 通过校准点选择控制精度
3. **操作简单**: 通过Zigbee命令远程操作
4. **安全可靠**: 完整的备份恢复机制

## 🛠️ 故障排除

### 常见问题

#### 1. 校准点1未设置
- **症状**: 执行ID=5时提示"Calib point 1 not set!"
- **解决**: 先执行ID=4设置第一个校准点

#### 2. 校准点距离太近
- **症状**: 提示"Calib points too close!"
- **解决**: 选择距离更远的两个校准点（建议>100cm）

#### 3. MID360数据无效
- **症状**: 提示"MID360 data invalid!"
- **解决**: 检查MID360连接状态和工作状态

#### 4. 校准结果异常
- **症状**: 缩放系数或偏移量明显异常
- **解决**: 使用ID=2恢复原始数据，重新校准

### 调试命令
```c
// 查看校准状态（需要在代码中调用）
zigbee_get_calibration_status();

// 查看关键坐标
AnoPTv8SendValStr(LT_D_IMU, ANOPTV8DEVID_SWJ, (float)work_pos[0][0], "Work[0] X:");
AnoPTv8SendValStr(LT_D_IMU, ANOPTV8DEVID_SWJ, (float)work_pos[28][0], "End X:");
```

## 📋 命令参考表

| 命令 | 功能 | 参数 | 回复 | 说明 |
|------|------|------|------|------|
| AA FF 01 00 EA | 启动任务 | ID=0 | 01/FE/FF | 货物遍历任务启动 |
| AA FF 03 01 EA | 备份数据 | ID=1 | 01 | 备份work_pos到backup |
| AA FF 03 02 EA | 恢复数据 | ID=2 | 02 | 从backup恢复work_pos |
| AA FF 03 03 EA | 终点校准 | ID=3 | 03/FF | 单点校准终点坐标 |
| AA FF 03 04 EA | 设置点1 | ID=4 | 04/FF | 两点校准第一点 |
| AA FF 03 05 EA | 执行校准 | ID=5 | 05/FF | 两点校准执行 |

### 回复代码说明
- **01-05**: 对应命令执行成功
- **FE**: 无效ID
- **FF**: 执行失败（通常是MID360数据无效）
- **FD**: 未知命令

---

**实现完成日期**: 2025-01-13
**版本**: v2.0 (高级校准系统)
**作者**: Augment Agent
**状态**: 已实现，建议测试验证
