# TOF双传感器完整场景测试方案
**版权：米醋电子工作室**  
**测试日期：2024年**  
**测试目标：验证定高抗干扰和避障快速响应**

## 🎯 测试目标

### 定高传感器测试目标
- ✅ 验证抗地面异物干扰能力 (石子、草叶等)
- ✅ 验证高度测量稳定性和精度
- ✅ 验证鲁棒平均算法的有效性

### 避障传感器测试目标
- ✅ 验证快速响应障碍物变化
- ✅ 验证最小值算法检测最近障碍物
- ✅ 验证3点时域滤波的响应速度

## 🧪 测试场景设计

### 场景1：定高传感器抗干扰测试

#### 测试环境设置
```c
// 测试配置
void setup_altitude_interference_test(void) {
    tof_init_dual_sensors();
    
    // 确认传感器0配置为定高模式
    printf("传感器0滤波类型: %d (应为%d)\n", 
           tof_sensors[0].filter_type, TOF_FILTER_ALTITUDE);
    printf("传感器0滤波窗口: %d点\n", tof_sensors[0].filter_size);
}
```

#### 测试用例1.1：平整地面基准测试
```c
void test_clean_ground_baseline(void) {
    printf("=== 平整地面基准测试 ===\n");
    
    uint16_t measurements[50];
    uint32_t sum = 0;
    
    for (int i = 0; i < 50; i++) {
        tof_update();
        measurements[i] = tof_get_distance_cm(0);
        sum += measurements[i];
        printf("测量%d: %u cm\n", i+1, measurements[i]);
        delay_ms(100);
    }
    
    uint16_t average = sum / 50;
    uint16_t max_deviation = 0;
    
    for (int i = 0; i < 50; i++) {
        uint16_t deviation = abs(measurements[i] - average);
        if (deviation > max_deviation) {
            max_deviation = deviation;
        }
    }
    
    printf("平均高度: %u cm\n", average);
    printf("最大偏差: %u cm\n", max_deviation);
    printf("稳定性: %s\n", (max_deviation < 5) ? "优秀" : "需改进");
}
```

#### 测试用例1.2：地面异物干扰测试
```c
void test_ground_interference(void) {
    printf("=== 地面异物干扰测试 ===\n");
    printf("请在传感器下方放置石子、草叶等异物\n");
    printf("按任意键开始测试...\n");
    getchar();
    
    uint16_t clean_baseline = 0;
    uint16_t interference_measurements[30];
    
    // 获取干净地面基准
    for (int i = 0; i < 10; i++) {
        tof_update();
        clean_baseline += tof_get_distance_cm(0);
        delay_ms(100);
    }
    clean_baseline /= 10;
    
    printf("基准高度: %u cm\n", clean_baseline);
    printf("开始异物干扰测试...\n");
    
    // 异物干扰测试
    for (int i = 0; i < 30; i++) {
        tof_update();
        interference_measurements[i] = tof_get_distance_cm(0);
        uint16_t deviation = abs(interference_measurements[i] - clean_baseline);
        
        printf("测量%d: %u cm (偏差: %u cm)\n", 
               i+1, interference_measurements[i], deviation);
        delay_ms(100);
    }
    
    // 分析抗干扰效果
    uint16_t large_deviations = 0;
    for (int i = 0; i < 30; i++) {
        if (abs(interference_measurements[i] - clean_baseline) > 10) {
            large_deviations++;
        }
    }
    
    printf("大偏差次数: %u/30\n", large_deviations);
    printf("抗干扰性能: %s\n", (large_deviations < 5) ? "优秀" : "需改进");
}
```

### 场景2：避障传感器快速响应测试

#### 测试环境设置
```c
void setup_obstacle_response_test(void) {
    // 确认传感器1配置为避障模式
    printf("传感器1滤波类型: %d (应为%d)\n", 
           tof_sensors[1].filter_type, TOF_FILTER_OBSTACLE);
    printf("传感器1滤波窗口: %d点\n", tof_sensors[1].filter_size);
}
```

#### 测试用例2.1：静态障碍物检测
```c
void test_static_obstacle_detection(void) {
    printf("=== 静态障碍物检测测试 ===\n");
    printf("请在传感器前方放置障碍物 (距离50cm-200cm)\n");
    printf("按任意键开始测试...\n");
    getchar();
    
    for (int i = 0; i < 20; i++) {
        tof_update();
        uint16_t distance = tof_get_distance_cm(1);
        bool valid = tof_is_distance_valid(1);
        
        printf("测量%d: %u cm (有效: %s)\n", 
               i+1, distance, valid ? "是" : "否");
        delay_ms(100);
    }
}
```

#### 测试用例2.2：动态障碍物响应测试
```c
void test_dynamic_obstacle_response(void) {
    printf("=== 动态障碍物响应测试 ===\n");
    printf("请手动移动障碍物，从远到近再到远\n");
    printf("按任意键开始测试...\n");
    getchar();
    
    uint16_t prev_distance = 0;
    uint16_t response_delays[50];
    uint8_t delay_count = 0;
    
    for (int i = 0; i < 50; i++) {
        tof_update();
        uint16_t current_distance = tof_get_distance_cm(1);
        
        // 检测距离变化
        if (prev_distance > 0) {
            uint16_t change = abs(current_distance - prev_distance);
            if (change > 10) {  // 显著变化
                response_delays[delay_count++] = i;
                printf("检测到显著变化: %u->%u cm (响应时间: %d*100ms)\n", 
                       prev_distance, current_distance, i);
            }
        }
        
        printf("测量%d: %u cm\n", i+1, current_distance);
        prev_distance = current_distance;
        delay_ms(100);
    }
    
    printf("总响应次数: %u\n", delay_count);
    printf("响应速度: %s\n", (delay_count > 5) ? "优秀" : "需改进");
}
```

### 场景3：双传感器协同工作测试

#### 测试用例3.1：同时工作稳定性测试
```c
void test_dual_sensor_stability(void) {
    printf("=== 双传感器协同工作测试 ===\n");
    
    for (int i = 0; i < 100; i++) {
        tof_update();
        
        uint16_t altitude = tof_get_distance_cm(0);
        bool altitude_valid = tof_is_distance_valid(0);
        
        uint16_t obstacle = tof_get_distance_cm(1);
        bool obstacle_valid = tof_is_distance_valid(1);
        
        printf("循环%d - 定高: %u cm (%s), 避障: %u cm (%s)\n", 
               i+1, 
               altitude, altitude_valid ? "有效" : "无效",
               obstacle, obstacle_valid ? "有效" : "无效");
        
        delay_ms(50);  // 20Hz更新频率
    }
}
```

#### 测试用例3.2：性能基准测试
```c
void test_performance_benchmark(void) {
    printf("=== 性能基准测试 ===\n");
    
    uint32_t start_time, end_time;
    uint32_t total_cycles = 0;
    
    for (int i = 0; i < 1000; i++) {
        start_time = DWT->CYCCNT;
        tof_update();
        end_time = DWT->CYCCNT;
        
        total_cycles += (end_time - start_time);
    }
    
    uint32_t avg_cycles = total_cycles / 1000;
    float cpu_usage = (float)avg_cycles / 168000.0f; // @168MHz, 1ms = 168000 cycles
    
    printf("平均CPU周期: %lu\n", avg_cycles);
    printf("1ms任务占比: %.2f%%\n", cpu_usage * 100);
    printf("性能目标: %s\n", (cpu_usage < 0.02f) ? "达成" : "未达成");
}
```

## 📊 测试验收标准

### 定高传感器验收标准
| 测试项目 | 验收标准 | 测试方法 |
|---------|---------|----------|
| 基准稳定性 | 最大偏差 < 5cm | 平整地面50次测量 |
| 抗干扰性 | 大偏差次数 < 5/30 | 地面异物干扰测试 |
| 响应平滑性 | 相邻测量差值 < 3cm | 连续测量分析 |

### 避障传感器验收标准
| 测试项目 | 验收标准 | 测试方法 |
|---------|---------|----------|
| 检测精度 | 误差 < ±5cm | 已知距离对比 |
| 响应速度 | 检测延迟 < 300ms | 动态障碍物测试 |
| 最近物体优先 | 多物体时选择最近 | 多障碍物场景 |

### 系统性能验收标准
| 性能指标 | 验收标准 | 测试方法 |
|---------|---------|----------|
| CPU使用率 | < 2% (1ms任务) | 性能基准测试 |
| 双传感器稳定性 | 同时工作无冲突 | 协同工作测试 |
| 向后兼容性 | 现有API正常工作 | 兼容性验证 |

## 🔧 测试执行步骤

### 第一阶段：基础功能验证 (30分钟)
1. 编译代码，确认无错误
2. 运行基准测试，验证基本功能
3. 检查API兼容性

### 第二阶段：定高传感器测试 (45分钟)
1. 平整地面基准测试
2. 地面异物干扰测试
3. 稳定性分析

### 第三阶段：避障传感器测试 (45分钟)
1. 静态障碍物检测测试
2. 动态障碍物响应测试
3. 响应速度分析

### 第四阶段：系统集成测试 (30分钟)
1. 双传感器协同工作测试
2. 性能基准测试
3. 长时间稳定性测试

---
**测试总时间：2.5小时**  
**测试环境：实际硬件 + 真实场景**  
**验收标准：所有测试用例通过**
