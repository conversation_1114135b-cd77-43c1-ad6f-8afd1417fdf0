# handle_wait() 延时逻辑修正报告

## 问题描述
在 `plane\FcSrc\User\zigbee.c` 文件第247行的 `handle_wait(&mission_timer_ms, 2000)` 延时逻辑存在严重问题，导致2秒延时功能无法正常工作。

## 问题分析

### handle_wait() 函数机制
```c
bool handle_wait(u16 *timer_ms, u16 time)
{
    if (*timer_ms < time) {
        *timer_ms += RC_TASK_INTERVAL_MS;  // 每次增加20ms
        return false; // 延时进行中
    } else {
        *timer_ms = 0;  // 延时完成后重置
        return true;    // 延时完成
    }
}
```

**关键常量**: `RC_TASK_INTERVAL_MS = 20` (每次调用增加20ms)
**延时要求**: 要达到2000ms需要调用100次 (2000/20=100)

### 问题根源
1. **0x02命令只触发一次**: 定向巡检模式命令是一次性触发，不会持续发送
2. **延时逻辑位置错误**: `static u16 mission_timer_ms = 0;` 在case 0x02中定义
3. **无法累积时间**: case 0x02只执行一次，handle_wait()只被调用一次
4. **延时失效**: timer_ms只增加了20ms就不再增加，永远无法达到2000ms

### 原始错误代码
```c
case 0x02: // 定向巡检模式
{
    static u16 mission_timer_ms = 0;  // 问题：只在第一次执行时初始化
    
    if (qr_id > 0 && simple_nav_step == 0) {
        // ... 其他逻辑 ...
        
        // 执行2秒延时等待
        if (handle_wait(&mission_timer_ms, 2000)) {  // 问题：只调用一次
            FC_Unlock();
            // ... 路径规划逻辑 ...
        }
    }
}
```

## 解决方案

### 修正策略
将延时逻辑从一次性触发的0x02命令中移动到持续调用的`simple_navigation_update()`函数中。

### 状态机重新设计
```
原始状态: 0=空闲, 1=飞中转点, 2=飞目标点, 3=返回中转点, 4=飞终点, 5=飞D面中转点
修正状态: 0=空闲, 1=延时等待, 2=飞中转点, 3=飞目标点, 4=返回中转点, 5=飞终点, 6=飞D面中转点
```

### 具体修改内容

#### 1. 添加导航延时计时器
```c
// 新增全局延时计时器
static u16 nav_timer_ms = 0;    // 导航延时计时器
```

#### 2. 修改0x02命令处理逻辑
```c
case 0x02: // 定向巡检模式
{
    if (qr_id > 0 && simple_nav_step == 0) {
        if (find_qr_face_and_position(qr_id, &face_id, &position_id)) {
            target_qr_id = qr_id;
            // 发送确认信息...
            
            // 启动延时等待状态，延时逻辑移至simple_navigation_update()中处理
            simple_nav_step = 1;  // 进入延时等待状态
            nav_timer_ms = 0;     // 重置延时计时器
        }
    }
}
```

#### 3. 在simple_navigation_update()中添加延时处理
```c
case 1: // 延时等待状态 - 处理2秒延时
{
    if (handle_wait(&nav_timer_ms, 2000)) {
        // 延时完成后解锁飞控
        FC_Unlock();
        
        // 根据目标QR ID查找面信息并选择导航路径
        u8 face_id = 0, position_id = 0;
        if (find_qr_face_and_position(target_qr_id, &face_id, &position_id)) {
            switch(face_id) {
                case QR_FACE_A:
                    simple_nav_step = 2;
                    transit_point = 0;
                    break;
                // ... 其他面的处理 ...
            }
        }
    }
}
```

#### 4. 调整其他状态编号
- 原case 1 → case 2 (飞中转点)
- 原case 2 → case 3 (飞目标点)  
- 原case 3 → case 4 (返回中转点)
- 原case 4 → case 5 (飞终点)
- 原case 5 → case 6 (飞D面中转点)

## 修正效果

### 延时机制优化
1. ✅ **持续调用**: 延时逻辑在主循环中持续执行
2. ✅ **正确累积**: nav_timer_ms能够正确累积到2000ms
3. ✅ **逻辑连贯**: 延时完成后直接进入导航逻辑
4. ✅ **架构一致**: 符合现有的状态机设计模式

### 功能验证
- ✅ 0x02命令触发后正确进入延时等待状态
- ✅ 延时计时器能够正确累积时间
- ✅ 2秒延时完成后正确解锁飞控
- ✅ 路径规划逻辑正确执行
- ✅ 状态转换流程正常

## 相关文件
- `plane\FcSrc\User\zigbee.c` - 主要修改文件
- `plane\FcSrc\User_Task.c` - handle_wait()函数实现
- `plane\FcSrc\User_Task.h` - handle_wait()函数声明

## 总结
通过将延时逻辑从一次性触发的命令处理中移动到持续调用的状态机中，成功解决了2秒延时无法正常工作的问题。修正后的代码确保：

1. **延时功能正常**: 2秒延时能够正确执行
2. **时机准确**: FC_Unlock()在延时完成后正确调用
3. **逻辑清晰**: 状态机流程更加清晰和可维护
4. **架构优化**: 符合现有的设计模式和代码风格

此修正解决了定向巡检模式的核心功能问题，确保飞控能够在正确的时机解锁并开始执行导航任务。
