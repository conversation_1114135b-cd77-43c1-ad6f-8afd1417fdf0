*** Using Compiler 'V5.06 update 7 (build 960)', folder: 'D:\keil5\ARM\ARMCC\Bin'
Build target 'Ano_LX'
compiling User_Task.c...
..\FcSrc\User_Task.c(767): warning:  #223-D: function "execute_path_planning" declared implicitly
              if (execute_path_planning()) {
..\FcSrc\User_Task.c(772): error:  #20: identifier "ANOLOGCOLOR_YELLOW" is undefined
                  AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_YELLOW, "Path planning failed, using default path");
..\FcSrc\User_Task.c(773): warning:  #223-D: function "setup_default_patrol_path" declared implicitly
                  setup_default_patrol_path();
..\FcSrc\User_Task.c(781): warning:  #223-D: function "check_mission_timeout" declared implicitly
              if (check_mission_timeout()) {
..\FcSrc\User_Task.c(787): warning:  #223-D: function "get_next_patrol_point" declared implicitly
              int next_point = get_next_patrol_point();
..\FcSrc\User_Task.c(793): warning:  #223-D: function "sprintf" declared implicitly
                  sprintf(debug_str, "Next patrol point: %d (pos_code: %d)",
..\FcSrc\User_Task.c(807): warning:  #223-D: function "check_mission_timeout" declared implicitly
              if (check_mission_timeout()) {
..\FcSrc\User_Task.c(827): warning:  #223-D: function "sprintf" declared implicitly
                  sprintf(debug_str, "Arrived and stabilized at patrol point %d", current_patrol_index);
..\FcSrc\User_Task.c(836): warning:  #223-D: function "check_mission_timeout" declared implicitly
              if (check_mission_timeout()) {
..\FcSrc\User_Task.c(852): error:  #20: identifier "ANOLOGCOLOR_YELLOW" is undefined
                      AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_YELLOW, "Laser adjusting to target");
..\FcSrc\User_Task.c(861): warning:  #223-D: function "sprintf" declared implicitly
                  sprintf(point_info, "Patrolling point %d (pos_code: %d) - Laser active",
..\FcSrc\User_Task.c(877): warning:  #223-D: function "mark_patrol_point_completed" declared implicitly
                  mark_patrol_point_completed(current_patrol_index);
..\FcSrc\User_Task.c(890): warning:  #223-D: function "is_patrol_complete" declared implicitly
              if (is_patrol_complete() || check_mission_timeout()) {
..\FcSrc\User_Task.c(890): warning:  #223-D: function "check_mission_timeout" declared implicitly
              if (is_patrol_complete() || check_mission_timeout()) {
..\FcSrc\User_Task.c(894): warning:  #223-D: function "get_patrol_statistics" declared implicitly
                  get_patrol_statistics(&completed, &total, &elapsed_ms);
..\FcSrc\User_Task.c(897): warning:  #223-D: function "sprintf" declared implicitly
                  sprintf(stats_str, "Patrol complete: %d/%d points, %lu s elapsed",
..\FcSrc\User_Task.c(934): error:  #20: identifier "ANOLOGCOLOR_YELLOW" is undefined
                  AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_YELLOW, "45° descent failed, using normal landing");
..\FcSrc\User_Task.c(996): warning:  #223-D: function "get_patrol_statistics" declared implicitly
              get_patrol_statistics(&completed, &total, &elapsed_ms);
..\FcSrc\User_Task.c(999): warning:  #223-D: function "sprintf" declared implicitly
              sprintf(final_stats, "Mission complete! %d/%d points patrolled in %lu seconds",
..\FcSrc\User_Task.c(1057): warning:  #177-D: variable "ch8_value"  was declared but never referenced
  	uint16_t ch8_value = rc_in.rc_ch.st_data.ch_[ch_8_aux4];
..\FcSrc\User_Task.c(1088): error:  #159: declaration is incompatible with previous "execute_path_planning"  (declared at line 767)
  bool execute_path_planning(void)
..\FcSrc\User_Task.c(1099): warning:  #223-D: function "get_current_no_fly_zones" declared implicitly
      int no_fly_count = get_current_no_fly_zones(no_fly_zones);
..\FcSrc\User_Task.c(1205): error:  #159: declaration is incompatible with previous "mark_patrol_point_completed"  (declared at line 877)
  void mark_patrol_point_completed(int index)
..\FcSrc\User_Task.c(1212): warning:  #223-D: function "sprintf" declared implicitly
          sprintf(debug_str, "Patrol point %d completed (pos_code: %d)",
..\FcSrc\User_Task.c(1236): error:  #159: declaration is incompatible with previous "is_patrol_complete"  (declared at line 890)
  bool is_patrol_complete(void)
..\FcSrc\User_Task.c(1253): warning:  #223-D: function "sprintf" declared implicitly
      sprintf(debug_str, "Patrol progress: %d/%d points completed",
..\FcSrc\User_Task.c(1265): error:  #159: declaration is incompatible with previous "check_mission_timeout"  (declared at line 781)
  bool check_mission_timeout(void)
..\FcSrc\User_Task.c(1273): warning:  #223-D: function "sprintf" declared implicitly
          sprintf(debug_str, "Mission timeout: %lu ms elapsed", elapsed_time);
..\FcSrc\User_Task.c(1283): warning:  #223-D: function "sprintf" declared implicitly
          sprintf(debug_str, "Mission time: %lu s / 300 s", elapsed_time / 1000);
..\FcSrc\User_Task.c(1294): error:  #159: declaration is incompatible with previous "setup_default_patrol_path"  (declared at line 773)
  void setup_default_patrol_path(void)
..\FcSrc\User_Task.c(1296): error:  #20: identifier "ANOLOGCOLOR_YELLOW" is undefined
      AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_YELLOW, "Setting up default patrol path");
..\FcSrc\User_Task.c(1326): error:  #159: declaration is incompatible with previous "get_patrol_statistics"  (declared at line 894)
  void get_patrol_statistics(int *completed, int *total, uint32_t *elapsed_ms)
..\FcSrc\User_Task.c(1374): warning:  #223-D: function "sprintf" declared implicitly
      sprintf(debug_str, "45° descent: start=%.1fcm, target=%.1fcm, distance=%.1fcm",
..\FcSrc\User_Task.c(1427): warning:  #223-D: function "sprintf" declared implicitly
              sprintf(debug_str, "45° descent: XY_dist=%.1fcm, Z=%.1fcm, target_Z=%.1fcm",
..\FcSrc\User_Task.c(1453): warning:  #223-D: function "sprintf" declared implicitly
              sprintf(debug_str, "Approaching descent point: XY_dist=%.1fcm", xy_distance);
..\FcSrc\User_Task.c(1482): warning:  #1-D: last line of file ends without a newline
  }
..\FcSrc\User_Task.c(49): warning:  #177-D: function "execute_landing_sequence"  was declared but never referenced
  static void execute_landing_sequence(u16 *timer_ms, u8 *land_flag);
..\FcSrc\User_Task.c(1467): warning:  #177-D: function "calculate_descent_progress"  was declared but never referenced
  static float calculate_descent_progress(void)
..\FcSrc\User_Task.c(92): warning:  #177-D: variable "dadian_f"  was declared but never referenced
  static u8 dadian_f = 0;
..\FcSrc\User_Task.c(108): warning:  #550-D: variable "descent_angle"  was set but never used
  static float descent_angle = 45.0f;            // 降落角度
..\FcSrc\User_Task.c(262): warning:  #177-D: function "is_dadian_down_command"  was declared but never referenced
  static inline bool is_dadian_down_command(uint16_t ch_value) {
..\FcSrc\User_Task.c(286): warning:  #177-D: function "is_CAM_reached"  was declared but never referenced
  static inline bool is_CAM_reached(void) {
..\FcSrc\User_Task.c(347): warning:  #177-D: function "clear_actual_work_positions"  was declared but never referenced
  static void clear_actual_work_positions(void)
..\FcSrc\User_Task.c(360): warning:  #177-D: function "validate_work_positions"  was declared but never referenced
  static bool validate_work_positions(void)
..\FcSrc\User_Task.c(672): warning:  #177-D: function "handle_return_home"  was declared but never referenced
  static void handle_return_home(void)
..\FcSrc\User_Task.c: 35 warnings, 10 errors
compiling zigbee.c...
..\FcSrc\User\zigbee.c(52): warning:  #550-D: variable "animal_records"  was set but never used
  static animal_record_t animal_records[MAX_ANIMAL_RECORDS];  // 动物发现记录数组
..\FcSrc\User\zigbee.c(59): warning:  #550-D: variable "coordinate_offset_x"  was set but never used
  static s16 coordinate_offset_x = 0;        // X轴偏移量
..\FcSrc\User\zigbee.c(60): warning:  #550-D: variable "coordinate_offset_y"  was set but never used
  static s16 coordinate_offset_y = 0;        // Y轴偏移量
..\FcSrc\User\zigbee.c: 3 warnings, 0 errors
".\build\ANO_LX.axf" - 10 Error(s), 38 Warning(s).
Target not created.
Build Time Elapsed:  00:00:02
