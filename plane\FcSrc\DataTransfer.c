#include "DataTransfer.h"
#include "AnoPTv8ExAPI.h"
#include "LX_LowLevelFunc.h"
#include "Drv_RcIn.h"
#include "LX_ExtSensor.h"
#include "Drv_led.h"
#include "LX_FcState.h"
#include "Drv_Uart.h"
#include "AnoPTv8.h"
#include "AnoPTv8Run.h"
//#include "Tofsense-m.h"
#include "mid360.h"
#include "PID.h"
#include "Maixcam.h"
#include "tofmini.h"
#include "User_Task.h"


/*==========================================================================
 * 描述    ：凌霄飞控通信主程序
 * 更新时间：2024-12-25
 * 作者		 ：匿名科创-茶不思
 * 官网    ：www.anotc.com
 * 淘宝    ：anotc.taobao.com
 * 技术Q群 ：190169595
 * 项目合作：18084888982，18061373080
============================================================================
 * 匿名科创团队感谢大家的支持，欢迎大家进群互相交流、讨论、学习。
 * 若您觉得匿名有不好的地方，欢迎您拍砖提意见。
 * 若您觉得匿名好，请多多帮我们推荐，支持我们。
 * 匿名开源程序代码欢迎您的引用、延伸和拓展，不过在希望您在使用时能注明出处。
 * 君子坦荡荡，小人常戚戚，匿名坚决不会请水军、请喷子，也从未有过抹黑同行的行为。
 * 开源不易，生活更不容易，希望大家互相尊重、互帮互助，共同进步。
 * 只有您的支持，匿名才能做得更好。
===========================================================================*/

/* 
 * 帧发送优化说明：
 * 1. 所有重要帧设置固定周期，确保基础发送频率（即使无数据变化也会定期发送）
 * 2. 实时控制帧(0x41)获得最高优先级，确保控制命令及时响应
 * 3. 使用混合调度策略：优先级+轮询指针，平衡实时性和公平性
 * 4. 保留按需触发机制，允许数据变化时立即发送（可能导致实际频率高于周期设定）
 */

// 自动发送帧信息配置
// 格式：{帧ID, 发送周期(ms)}，周期为0表示仅按需发送
const _st_autoSendInfo ASFInfo[] =
{
    {0x41, 15},   // 实时控制帧，15ms周期(~67Hz)，最高优先级
    {0x40, 20},   // 遥控数据，20ms周期(50Hz)
    {0x33, 20},   // 速度传感器数据，25ms周期(40Hz)
    {0x34, 20},   // 距离传感器数据，50ms周期(20Hz)
		{0xF1, 20},   // 灵活帧F1数据，20ms周期(50Hz)
		{0xF2, 20},   // 灵活帧F1数据，20ms周期(50Hz)
};

const uint16_t ASFCNT = sizeof(ASFInfo) / sizeof(_st_autoSendInfo);
static _st_autoSendSta ASFSta[ASFCNT];


//===================================================================

/**
 * @brief 自动发送帧检查函数 - 混合调度策略
 * 
 * 实现三阶段处理机制：
 * 1. 更新所有周期性帧的计时器
 * 2. 优先处理实时控制帧(0x41)
 * 3. 使用轮询指针法处理其他就绪帧
 */
void AutoSendFrameCheck(void)
{
    static uint8_t last_idx = 0;  // 轮询指针，记录上次处理位置
    uint8_t frame_sent = 0;       // 帧发送标志
    
    // 第一阶段：更新所有周期性帧的计时器
    for(int i=0; i<ASFCNT; i++)
    {
        if(ASFInfo[i].cycleTime > 0)
        {
            ASFSta[i].timeCnt++;
            if(ASFSta[i].timeCnt >= ASFInfo[i].cycleTime)
            {
                ASFSta[i].timeCnt = 0;
                ASFSta[i].readyToSend = 1;
            }
        }
    }
    
    // 第二阶段：优先处理实时控制帧(0x41)
    for(int i=0; i<ASFCNT; i++)
    {
        if(ASFInfo[i].fId == 0x41 && ASFSta[i].readyToSend)
        {
            ASFSta[i].readyToSend = 0;
            AnoDTLxFrameSend(ASFInfo[i].fId);
            frame_sent = 1;
            break;
        }
    }
    
    // 第三阶段：如果没有发送实时控制帧，使用轮询指针处理其他帧
    if(!frame_sent)
    {
        // 从上次处理位置开始，循环查找一圈
        for(int j=0; j<ASFCNT; j++)
        {
            // 计算当前检查的索引，实现循环查找
            int i = (last_idx + j) % ASFCNT;
            
            if(ASFSta[i].readyToSend)
            {
                ASFSta[i].readyToSend = 0;
                AnoDTLxFrameSend(ASFInfo[i].fId);
                // 更新下次开始查找的位置为当前位置的下一个
                last_idx = (i + 1) % ASFCNT;
                break;
            }
        }
    }
}

//void AutoSendFrameCheck(void)
//{
//    for(int i=0; i<ASFCNT; i++)
//    {
//        if(ASFInfo[i].cycleTime > 0)
//        {
//            ASFSta[i].timeCnt++;
//            if(ASFSta[i].timeCnt >= ASFInfo[i].cycleTime)
//            {
//                ASFSta[i].timeCnt = 0;
//                ASFSta[i].readyToSend = 1;
//            }
//        }
//        if(ASFSta[i].readyToSend)
//        {
//			ASFSta[i].readyToSend = 0;
//            AnoDTLxFrameSend(ASFInfo[i].fId);
//            return;
//        }
//    }
//}


/**
 * @brief 1ms周期任务函数
 * 
 * @param dT_s 时间间隔(秒)
 */
void AnoDTLxRunTask1Ms(float dT_s)
{
    AutoSendFrameCheck();
}

/**
 * @brief 帧数据解析函数
 * 
 * @param linktype 链路类型
 * @param p 帧数据指针
 */
void AnoDTLxFrameAnl(const uint8_t linktype, const _un_frame_v8 *p)
{
    uint8_t _fid = p->frame.frameid;
    switch(_fid)
    {
    case 0x00:
    {
        //校验数据
    }
    break;
    case 0x20:
    {
        //PWM数据
        pwm_to_esc.pwm_m1 = *((uint16_t  *)(p->frame.data));
        pwm_to_esc.pwm_m2 = *((uint16_t  *)(p->frame.data + 2));
        pwm_to_esc.pwm_m3 = *((uint16_t  *)(p->frame.data + 4));
        pwm_to_esc.pwm_m4 = *((uint16_t  *)(p->frame.data + 6));
//        pwm_to_esc.pwm_m5 = *((uint16_t  *)(p->frame.data + 8));
//        pwm_to_esc.pwm_m6 = *((uint16_t  *)(p->frame.data + 10));
//        pwm_to_esc.pwm_m7 = *((uint16_t  *)(p->frame.data + 12));
//        pwm_to_esc.pwm_m8 = *((uint16_t  *)(p->frame.data + 14));
    }
    break;
    case 0x0F:
    {
        //凌霄IMU发出的RGB灯光数据
    }
    break;
    case 0x06:
    {
        //凌霄飞控当前的运行状态
        fc_sta.fc_mode_sta = *(p->frame.data);
        fc_sta.unlock_sta = *(p->frame.data + 1);
        fc_sta.cmd_fun.CID = *(p->frame.data + 2);
        fc_sta.cmd_fun.CMD_0 = *(p->frame.data + 3);
        fc_sta.cmd_fun.CMD_1 = *(p->frame.data + 4);
    }
    break;
    case 0x07:
    {
        //飞行速度
        for(uint8_t i=0; i<6; i++)
        {
            fc_vel.byte_data[i] = *(p->frame.data + i);
        }
    }
    break;
    case 0x03:
    {
        //姿态角（需要在上位机凌霄IMU界面配置输出功能）
        for(uint8_t i=0; i<7; i++)
        {
            fc_att.byte_data[i] = *(p->frame.data + i);
        }
    }
    break;
    case 0x04:
    {
        //姿态四元数
        for(uint8_t i=0; i<9; i++)
        {
            fc_att_qua.byte_data[i] = *(p->frame.data + i);
        }
    }
    break;
    case 0x05:
    {
        //高度数据
        for(uint8_t i=0; i<9; i++)
        {
            fc_alt.byte_data[i] = *(p->frame.data + i);
        }
    }
    break;
    case 0x01:
    {
        //传感器数据
    }
    break;
    }
}

/**
 * @brief 帧数据发送函数
 * 
 * @param fid 帧ID
 */
void AnoDTLxFrameSend(const uint8_t fid)
{
    // 查找帧索引
    int idx = -1;
    for(int i=0; i<ASFCNT; i++) {
        if(ASFInfo[i].fId == fid) {
            idx = i;
            break;
        }
    }
    
    
    switch (fid)
    {
    case 0x30: //GPS数据
    {
        uint8_t _sbuf[23];
        memcpy(_sbuf,ext_sens.fc_gps.byte,23);
        AnoPTv8SendBuf(LT_D_IMU, ANOPTV8DEVID_LXIMU, fid, ANOPTV8TXPRI_DATA, _sbuf, sizeof(_sbuf));
    }
    break;
    case 0x33: //通用速度测量数据
    {
        uint8_t _sbuf[6];
        memcpy(_sbuf,ext_sens.gen_vel.byte,6);
        AnoPTv8SendBuf(LT_D_IMU, ANOPTV8DEVID_LXIMU, fid, ANOPTV8TXPRI_DATA, _sbuf, sizeof(_sbuf));
    }
    break;
    case 0x34: //通用距离测量数据
    {
        uint8_t _sbuf[7];
        memcpy(_sbuf,ext_sens.gen_dis.byte,7);
        AnoPTv8SendBuf(LT_D_IMU, ANOPTV8DEVID_LXIMU, fid, ANOPTV8TXPRI_DATA, _sbuf, sizeof(_sbuf));
    }
    break;
    case 0x40: //遥控数据帧
    {
        uint8_t _sbuf[20];
        memcpy(_sbuf,rc_in.rc_ch.byte_data,20);
        AnoPTv8SendBuf(LT_D_IMU, ANOPTV8DEVID_LXIMU, fid, ANOPTV8TXPRI_DATA, _sbuf, sizeof(_sbuf));
    }
    break;
    case 0x41: //实时控制数据帧
    {
        uint8_t _sbuf[14];
        memcpy(_sbuf,rt_tar.byte_data,14);
        AnoPTv8SendBuf(LT_D_IMU, ANOPTV8DEVID_LXIMU, fid, ANOPTV8TXPRI_DATA, _sbuf, sizeof(_sbuf));
    }
    break;
    case 0xF1: //综合导航数据帧 - 包含位置、目标、TOF、摄像头数据
    {
        uint8_t _sbuf[20]; // 10个s16数据项，每个2字节，共20字节
        uint8_t offset = 0;

        // 1. mid360.pose_x_cm (当前X位置) - s16, 2字节
        memcpy(_sbuf + offset, &mid360.pose_x_cm, 2);
        offset += 2;

        // 2. target_pos[0] (目标X位置) - s16, 2字节
        memcpy(_sbuf + offset, &target_pos[0], 2);
        offset += 2;

        // 3. mid360.pose_y_cm (当前Y位置) - s16, 2字节
        memcpy(_sbuf + offset, &mid360.pose_y_cm, 2);
        offset += 2;

        // 4. target_pos[1] (目标Y位置) - s16, 2字节
        memcpy(_sbuf + offset, &target_pos[1], 2);
        offset += 2;

//        // 5. tof_sensors[0].distance_cm (TOF距离) - uint16_t, 2字节
//        memcpy(_sbuf + offset, &tof_sensors[0].distance_cm, 2);
//        offset += 2;

        // 5. tof_sensors[0].distance_cm (TOF距离) - uint16_t, 2字节
        memcpy(_sbuf + offset, & g_tfmini_sensor.distance_cm, 2);
        offset += 2;



        // 6. target_pos[2] (目标Z位置/高度) - s16, 2字节
        memcpy(_sbuf + offset, &target_pos[2], 2);
        offset += 2;

        // 7. mid360.pose_yaw (当前偏航角) - s16, 2字节
        memcpy(_sbuf + offset, &mid360.pose_yaw, 2);
        offset += 2;

        // 8. target_pos[3] (目标偏航角) - s16, 2字节
        memcpy(_sbuf + offset, &target_pos[3], 2);
        offset += 2;

        // 9. maixcam.x (摄像头X坐标) - s16, 2字节
        s16 cam_x_temp = maixcam.x;  // 解决__packed类型兼容性问题
        memcpy(_sbuf + offset, &cam_x_temp, 2);
        offset += 2;

        // 10. target_cam[0] (摄像头目标X坐标) - s16, 2字节
        memcpy(_sbuf + offset, &target_cam[0], 2);
        offset += 2;

        AnoPTv8SendBuf(LT_D_IMU, ANOPTV8DEVID_SWJ, fid, ANOPTV8TXPRI_DATA, _sbuf, sizeof(_sbuf));
    }
    break;
		
		    case 0xF2: //综合导航数据帧 - 包含位置、目标、TOF、摄像头数据
    {
        uint8_t _sbuf[25]; // 4个float数据项，每个4字节，共20字节
        uint8_t offset = 0;

			
				// 1. maixcam.y (摄像头Y坐标) - s16, 2字节
				s16 cam_y_temp = maixcam.y;
				memcpy(_sbuf + offset, &cam_y_temp, 2);
				offset += 2;
				
			// 2. target_cam[0] (摄像头目标Y坐标) - s16, 2字节
				memcpy(_sbuf + offset, &target_cam[1], 2);
				offset += 2;
			
			// 3. PID_V[0] (PID计算值) - float, 4字节
        memcpy(_sbuf + offset, &PID_V[0], 4);
        offset += 4;
			// 4. PID_V[1] (PID计算值) - float, 4字节
        memcpy(_sbuf + offset, &PID_V[1], 4);
        offset += 4;

			// 5. PID_V[2] (PID计算值) - float, 4字节
        memcpy(_sbuf + offset, &PID_V[2], 4);
        offset += 4;

       // 6. PID_V[3] (PID计算值) - float, 4字节
        memcpy(_sbuf + offset, &PID_V[3], 4);
        offset += 4;
				
				// 7. 任务步数 - U8, 1字节
        memcpy(_sbuf + offset, &mission_step, 1);
        offset += 1;
				
				// 8. X速度 - s16, 2字节
        memcpy(_sbuf + offset, &mid360.speed_x_cms, 2);
        offset += 2;
				
				// 9. Y速度 - s16, 2字节
        memcpy(_sbuf + offset, &mid360.speed_y_cms, 2);
        offset += 2;
				
        AnoPTv8SendBuf(LT_D_IMU, ANOPTV8DEVID_SWJ, fid, ANOPTV8TXPRI_DATA, _sbuf, sizeof(_sbuf));
    }
    break;
		
    default:
        break;
    }
}

/**
 * @brief 触发帧发送函数 - 按需触发机制
 * 
 * 用于在数据变化时立即触发帧发送，不受周期限制
 * 
 * @param fid 要触发发送的帧ID
 */
void AnoDTLxFrameSendTrigger(const uint8_t fid)
{
    for(int i=0; i<ASFCNT; i++)
    {
        if(fid == ASFInfo[i].fId)
        {
            ASFSta[i].readyToSend = 1;
            // 注意：不重置计时器，保持周期性发送的节奏
        }
    }
}



