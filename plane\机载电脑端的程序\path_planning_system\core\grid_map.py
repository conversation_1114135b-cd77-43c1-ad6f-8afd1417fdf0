#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网格地图数据结构 - 7×9网格地图管理
版权信息：米醋电子工作室
创建日期：2025-07-30

功能描述：
管理7×9网格地图，处理禁飞区约束，提供坐标转换功能。
基于NumPy优化，提供高效的地图操作接口。
"""

import numpy as np
from typing import List, Tuple, Optional

class GridMap:
    """
    7×9网格地图管理器
    
    坐标系统：
    - 网格大小：9列×7行 (63个点)
    - A代表列(A1-A9)，B代表行(B1-B7)
    - 坐标映射：A1B1(11) -> (0,0), A9B7(97) -> (6,8)
    - position_code格式：列号×10+行号
    """
    
    # 网格常量 - 正确的坐标系统
    GRID_ROWS = 7    # 7行：B1到B7
    GRID_COLS = 9    # 9列：A1到A9
    TOTAL_POINTS = GRID_ROWS * GRID_COLS  # 63个点
    
    # 地图状态常量
    FREE_SPACE = 0      # 自由空间
    NO_FLY_ZONE = 1     # 禁飞区
    START_POINT = 2     # 起始点
    GOAL_POINT = 3      # 目标点
    
    def __init__(self):
        """初始化7×9网格地图"""
        # 性能优化：预分配NumPy数组
        self.grid = np.zeros((self.GRID_ROWS, self.GRID_COLS), dtype=np.uint8)
        self.position_codes = np.zeros((self.GRID_ROWS, self.GRID_COLS), dtype=np.uint16)
        
        # 预计算position_code映射表
        self._init_position_codes()
        
        # 预分配坐标转换缓冲区
        self._coord_buffer = np.zeros(2, dtype=np.int32)
        
    def _init_position_codes(self):
        """预计算position_code映射表 - 正确的坐标系统"""
        for row in range(self.GRID_ROWS):
            for col in range(self.GRID_COLS):
                # position_code = (col+1) * 10 + (row+1)
                # A1B1 = 11, A9B1 = 91, A1B7 = 17, A9B7 = 97
                self.position_codes[row, col] = (col + 1) * 10 + (row + 1)
    
    def position_code_to_grid(self, position_code: int) -> Tuple[int, int]:
        """
        将position_code转换为网格坐标 - 正确的坐标系统

        参数:
            position_code: 位置编码 (如11表示A1B1, 97表示A9B7)

        返回:
            (row, col): 网格坐标，如果无效返回(-1, -1)
        """
        if position_code < 11 or position_code > 97:
            return (-1, -1)

        # 解析position_code
        col_num = position_code // 10  # A列号 (1-9)
        row_num = position_code % 10   # B行号 (1-7)

        if col_num < 1 or col_num > 9 or row_num < 1 or row_num > 7:
            return (-1, -1)

        # 转换为数组索引
        row = row_num - 1  # B1->0, B2->1, ..., B7->6
        col = col_num - 1  # A1->0, A2->1, ..., A9->8

        return (row, col)
    
    def grid_to_position_code(self, row: int, col: int) -> int:
        """
        将网格坐标转换为position_code
        
        参数:
            row, col: 网格坐标
            
        返回:
            position_code: 位置编码，如果无效返回0
        """
        if row < 0 or row >= self.GRID_ROWS or col < 0 or col >= self.GRID_COLS:
            return 0
            
        return self.position_codes[row, col]
    
    def set_no_fly_zones(self, no_fly_zones: List[int]) -> bool:
        """
        设置禁飞区
        
        参数:
            no_fly_zones: 禁飞区position_code列表
            
        返回:
            bool: 设置是否成功
        """
        # 清除之前的禁飞区标记
        self.grid[self.grid == self.NO_FLY_ZONE] = self.FREE_SPACE
        
        valid_zones = 0
        for position_code in no_fly_zones:
            row, col = self.position_code_to_grid(position_code)
            if row >= 0 and col >= 0:
                self.grid[row, col] = self.NO_FLY_ZONE
                valid_zones += 1
        
        return valid_zones == len(no_fly_zones)
    
    def validate_continuous_zones(self, no_fly_zones: List[int]) -> bool:
        """
        验证禁飞区连续性（必须是3个连续的点）
        
        参数:
            no_fly_zones: 禁飞区position_code列表
            
        返回:
            bool: 是否连续
        """
        if len(no_fly_zones) != 3:
            return False
            
        # 转换为网格坐标
        coords = []
        for position_code in no_fly_zones:
            row, col = self.position_code_to_grid(position_code)
            if row < 0 or col < 0:
                return False
            coords.append((row, col))
        
        # 检查是否在同一行或同一列
        rows = [coord[0] for coord in coords]
        cols = [coord[1] for coord in coords]
        
        if len(set(rows)) == 1:  # 同一行
            cols.sort()
            return cols[1] - cols[0] == 1 and cols[2] - cols[1] == 1
        elif len(set(cols)) == 1:  # 同一列
            rows.sort()
            return rows[1] - rows[0] == 1 and rows[2] - rows[1] == 1
        
        return False
    
    def is_valid_position(self, row: int, col: int) -> bool:
        """检查位置是否有效且可通行"""
        if row < 0 or row >= self.GRID_ROWS or col < 0 or col >= self.GRID_COLS:
            return False
        return self.grid[row, col] != self.NO_FLY_ZONE
    
    def get_neighbors(self, row: int, col: int) -> List[Tuple[int, int]]:
        """
        获取相邻的可通行位置（8连通，考虑飞机尺寸的安全约束）

        参数:
            row, col: 当前位置

        返回:
            List[Tuple[int, int]]: 相邻可通行位置列表
        """
        neighbors = []

        # 8个方向的偏移量
        directions = [(-1, -1), (-1, 0), (-1, 1), (0, -1),
                     (0, 1), (1, -1), (1, 0), (1, 1)]

        for dr, dc in directions:
            new_row, new_col = row + dr, col + dc
            if self.is_valid_position(new_row, new_col):
                # 对于对角线移动，需要额外检查安全性
                if abs(dr) == 1 and abs(dc) == 1:  # 对角线移动
                    if self._is_diagonal_move_safe(row, col, new_row, new_col):
                        neighbors.append((new_row, new_col))
                else:  # 直线移动
                    neighbors.append((new_row, new_col))

        return neighbors

    def _is_diagonal_move_safe(self, from_row: int, from_col: int,
                              to_row: int, to_col: int) -> bool:
        """
        检查对角线移动是否安全（考虑飞机尺寸）

        参数:
            from_row, from_col: 起始位置
            to_row, to_col: 目标位置

        返回:
            bool: 移动是否安全
        """
        # 对角线移动会经过两个中间位置
        # 例如从(r1,c1)到(r2,c2)，会经过(r1,c2)和(r2,c1)
        intermediate_pos1 = (from_row, to_col)
        intermediate_pos2 = (to_row, from_col)

        # 检查中间位置是否为禁飞区
        if (intermediate_pos1[0] >= 0 and intermediate_pos1[0] < self.GRID_ROWS and
            intermediate_pos1[1] >= 0 and intermediate_pos1[1] < self.GRID_COLS):
            if self.grid[intermediate_pos1[0], intermediate_pos1[1]] == self.NO_FLY_ZONE:
                return False

        if (intermediate_pos2[0] >= 0 and intermediate_pos2[0] < self.GRID_ROWS and
            intermediate_pos2[1] >= 0 and intermediate_pos2[1] < self.GRID_COLS):
            if self.grid[intermediate_pos2[0], intermediate_pos2[1]] == self.NO_FLY_ZONE:
                return False

        return True
    
    def calculate_distance(self, pos1: Tuple[int, int], pos2: Tuple[int, int]) -> float:
        """
        计算两点间的欧几里得距离
        
        参数:
            pos1, pos2: 网格坐标
            
        返回:
            float: 距离值
        """
        dx = pos1[0] - pos2[0]
        dy = pos1[1] - pos2[1]
        return np.sqrt(dx * dx + dy * dy)
    
    def get_grid_copy(self) -> np.ndarray:
        """获取网格地图的副本"""
        return self.grid.copy()
    
    def reset(self):
        """重置地图为初始状态"""
        self.grid.fill(self.FREE_SPACE)

    def visualize_path(self, path_sequence: List[int], title: str = "路径可视化") -> str:
        """
        可视化路径规划结果 - 完全重写确保正确的坐标系统

        参数:
            path_sequence: position_code序列
            title: 图表标题

        返回:
            str: 可视化的文字图表
        """
        # 创建可视化网格 [行][列] = [B1-B7][A1-A9]
        vis_grid = [['.' for _ in range(self.GRID_COLS)] for _ in range(self.GRID_ROWS)]

        # 标记禁飞区
        for row in range(self.GRID_ROWS):
            for col in range(self.GRID_COLS):
                if self.grid[row, col] == self.NO_FLY_ZONE:
                    vis_grid[row][col] = '█'  # 禁飞区用实心方块

        # 标记路径
        for i, position_code in enumerate(path_sequence):
            row, col = self.position_code_to_grid(position_code)
            if row >= 0 and col >= 0:
                if i == 0:
                    vis_grid[row][col] = 'S'  # 起点
                elif i == len(path_sequence) - 1:
                    vis_grid[row][col] = 'E'  # 终点
                else:
                    # 使用数字表示访问顺序（只显示前9个）
                    if i <= 9:
                        vis_grid[row][col] = str(i)
                    else:
                        vis_grid[row][col] = '*'  # 后续点用*表示

        # 生成可视化字符串 - 完全重写确保正确
        result = f"\n{'='*60}\n{title}\n{'='*60}\n"
        result += "    "

        # 列标题：A1 A2 A3 A4 A5 A6 A7 A8 A9
        print(f"DEBUG: GRID_COLS = {self.GRID_COLS}")  # 调试输出
        for col in range(self.GRID_COLS):
            result += f"A{col+1} "
        result += "\n"

        # 行数据：B1到B7
        for row in range(self.GRID_ROWS):
            row_label = f"B{row+1}"  # B1, B2, B3, B4, B5, B6, B7
            result += f"{row_label}  "
            for col in range(self.GRID_COLS):
                result += f" {vis_grid[row][col]} "
            result += f"  {row_label}\n"

        # 底部列标题：A1 A2 A3 A4 A5 A6 A7 A8 A9
        result += "    "
        for col in range(self.GRID_COLS):
            result += f"A{col+1} "
        result += "\n"

        # 图例
        result += "\n图例说明:\n"
        result += "S = 起点, E = 终点, 1-9 = 访问顺序, * = 后续访问点\n"
        result += "█ = 禁飞区, . = 未访问点\n"
        result += f"路径长度: {len(path_sequence)} 个点\n"
        result += f"Position codes: {path_sequence[:15]}{'...' if len(path_sequence) > 15 else ''}\n"
        result += "="*60 + "\n"

        return result

    def visualize_path(self, path_sequence: List[int], title: str = "路径可视化") -> str:
        """
        可视化路径规划结果

        参数:
            path_sequence: position_code序列
            title: 图表标题

        返回:
            str: 可视化的文字图表
        """
        # 创建可视化网格
        vis_grid = [['.' for _ in range(self.GRID_COLS)] for _ in range(self.GRID_ROWS)]

        # 标记禁飞区
        for row in range(self.GRID_ROWS):
            for col in range(self.GRID_COLS):
                if self.grid[row, col] == self.NO_FLY_ZONE:
                    vis_grid[row][col] = '█'  # 禁飞区用实心方块

        # 标记路径
        for i, position_code in enumerate(path_sequence):
            row, col = self.position_code_to_grid(position_code)
            if row >= 0 and col >= 0:
                if i == 0:
                    vis_grid[row][col] = 'S'  # 起点
                elif i == len(path_sequence) - 1:
                    vis_grid[row][col] = 'E'  # 终点
                else:
                    # 使用数字表示访问顺序（只显示前9个）
                    if i <= 9:
                        vis_grid[row][col] = str(i)
                    else:
                        vis_grid[row][col] = '*'  # 后续点用*表示

        # 生成可视化字符串
        result = f"\n{'='*50}\n{title}\n{'='*50}\n"
        result += "   "

        # 列标题 (B1-B9)
        for col in range(self.GRID_COLS):
            result += f"B{col+1} "
        result += "\n"

        # 行数据
        for row in range(self.GRID_ROWS):
            row_label = f"A{7-row}"  # A7, A6, ..., A1
            result += f"{row_label} "
            for col in range(self.GRID_COLS):
                result += f" {vis_grid[row][col]} "
            result += f" {row_label}\n"

        # 底部列标题
        result += "   "
        for col in range(self.GRID_COLS):
            result += f"B{col+1} "
        result += "\n"

        # 图例
        result += "\n图例说明:\n"
        result += "S = 起点, E = 终点, 1-9 = 访问顺序, * = 后续访问点\n"
        result += "█ = 禁飞区, . = 未访问点\n"
        result += f"路径长度: {len(path_sequence)} 个点\n"
        result += f"Position codes: {path_sequence[:20]}{'...' if len(path_sequence) > 20 else ''}\n"
        result += "="*50 + "\n"

        return result
