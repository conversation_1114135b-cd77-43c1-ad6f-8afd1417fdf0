/*==========================================================================
 * 版权    ：米醋电子工作室
 * 创建时间：2024年
 * 作者    ：Bob (架构师)
 * 功能描述：TFmini单点激光传感器驱动头文件
 *
 * 主要功能：
 * 1. TFmini单点激光传感器专用驱动
 * 2. 实现TFmini 9字节协议解析状态机
 * 3. 简化的单点传感器接口设计
 * 4. 高效的数据处理和滤波算法
 *
 * TFmini协议规范：
 * - 数据帧格式（9字节）：
 *   Byte0: 0x59 (帧头1)
 *   Byte1: 0x59 (帧头2)
 *   Byte2: Dist_L (距离值低8位)
 *   Byte3: Dist_H (距离值高8位)
 *   Byte4: Strength_L (信号强度低8位)
 *   Byte5: Strength_H (信号强度高8位)
 *   Byte6: Temp_L (温度低8位)
 *   Byte7: Temp_H (温度高8位)
 *   Byte8: Checksum (前8字节累加和的低8位)
 *
 * 设计特点：
 * - 单点传感器专用设计，无需数组结构
 * - 简化的API接口，移除sensor_id参数
 * - 被动接收数据，无查询指令
===========================================================================*/

#ifndef TOFMINI_H
#define TOFMINI_H

#include "SysConfig.h"
#include <stdint.h>
#include <stdbool.h>

/*==============================================================================
 * TFmini协议常量定义
 *============================================================================*/
#define TFMINI_FRAME_HEADER1    0x59    // 帧头1
#define TFMINI_FRAME_HEADER2    0x59    // 帧头2
#define TFMINI_FRAME_LENGTH     9       // 数据帧长度
#define TFMINI_MAX_DISTANCE     1200    // 最大测距范围(cm)
#define TFMINI_MIN_STRENGTH     100     // 最小信号强度阈值
#define TFMINI_STRENGTH_INVALID 65535   // 信号强度无效值

// TFmini异常距离值定义
#define TFMINI_DIST_STRENGTH_LOW    65535   // 信号强度过低
#define TFMINI_DIST_STRENGTH_SAT    65534   // 信号强度饱和
#define TFMINI_DIST_AMBIENT_SAT     65532   // 环境光饱和

// TFmini滤波配置
#define TFMINI_FILTER_SIZE_MAX      8       // 最大滤波窗口大小
#define TFMINI_FILTER_SIZE_DEFAULT  4       // 默认滤波窗口大小



/*==============================================================================
 * TFmini协议解析状态机定义
 *============================================================================*/
typedef enum
{
    TFMINI_STATE_WAIT_HEADER1 = 0,     // 等待帧头1 (0x59)
    TFMINI_STATE_WAIT_HEADER2,         // 等待帧头2 (0x59)
    TFMINI_STATE_DIST_LOW,             // 接收距离低字节
    TFMINI_STATE_DIST_HIGH,            // 接收距离高字节
    TFMINI_STATE_STRENGTH_LOW,         // 接收信号强度低字节
    TFMINI_STATE_STRENGTH_HIGH,        // 接收信号强度高字节
    TFMINI_STATE_TEMP_LOW,             // 接收温度低字节
    TFMINI_STATE_TEMP_HIGH,            // 接收温度高字节
    TFMINI_STATE_CHECKSUM              // 接收校验和
} tfmini_parse_state_t;

/*==============================================================================
 * TFmini数据帧结构定义
 *============================================================================*/
typedef struct
{
    uint16_t distance;      // 距离值 (原始值，0-65535)
    uint16_t strength;      // 信号强度 (0-65535)
    uint16_t temperature;   // 温度值 (原始值)
    uint8_t checksum;       // 校验和
    bool is_valid;          // 数据有效性
} tfmini_frame_data_t;

/*==============================================================================
 * TFmini滤波算法枚举 - 单点传感器只需要时域滤波
 *============================================================================*/
typedef enum
{
    TFMINI_FILTER_MOVING_AVERAGE = 0    // 移动平均滤波（时域滤波）
} tfmini_filter_algorithm_t;

/*==============================================================================
 * TFmini传感器数据结构 - 单点传感器专用
 *============================================================================*/
typedef struct
{
    // 基本信息
    bool is_initialized;            // 初始化状态

    // 距离数据
    uint16_t distance_cm;           // 当前距离值(cm)
    bool is_distance_valid;         // 距离数据有效性

    // 信号质量
    uint16_t signal_strength;       // 当前信号强度
    uint8_t data_quality;           // 数据质量等级(0-3)

    // 状态检查
    uint8_t link_sta;               // 连接状态：0未连接，1已连接
    uint8_t work_sta;               // 工作状态：0异常，1正常
    uint16_t timeout_counter_ms;    // 超时计数器(ms)

    // 滤波配置
    tfmini_filter_algorithm_t filter_type;  // 滤波算法类型
    uint16_t filter_buffer[TFMINI_FILTER_SIZE_MAX];  // 时域滤波缓冲区
    uint8_t filter_index;           // 滤波索引
    uint8_t filter_size;            // 滤波窗口大小

    // 统计信息
    uint32_t frame_count;           // 接收帧计数
    uint32_t error_count;           // 错误帧计数
} tfmini_sensor_data_t;

/*==============================================================================
 * TFmini协议解析器结构定义
 *============================================================================*/
typedef struct
{
    tfmini_parse_state_t state;        // 当前解析状态
    uint8_t frame_buffer[TFMINI_FRAME_LENGTH];  // 数据帧缓冲区
    uint8_t data_index;                 // 数据索引
    uint8_t calculated_checksum;        // 计算的校验和
    tfmini_frame_data_t frame_data;     // 解析后的数据帧

    // 统计信息
    uint32_t frame_count;               // 接收帧计数
    uint32_t error_count;               // 错误帧计数
    uint32_t checksum_error_count;      // 校验和错误计数
} tfmini_parser_t;

/*==============================================================================
 * TFmini协议解析函数声明
 *============================================================================*/

/**
 * @brief 初始化TFmini协议解析器
 * @param parser 解析器指针
 */
void tfmini_parser_init(tfmini_parser_t *parser);

/**
 * @brief TFmini协议解析状态机 - 处理单个字节
 * @param parser 解析器指针
 * @param byte 接收的字节
 * @return true-完整帧解析完成, false-继续等待数据
 */
bool tfmini_parse_byte(tfmini_parser_t *parser, uint8_t byte);

/**
 * @brief 计算TFmini校验和
 * @param data 数据缓冲区指针
 * @param length 数据长度
 * @return 校验和值
 */
uint8_t tfmini_calculate_checksum(const uint8_t *data, uint8_t length);

/**
 * @brief 验证TFmini数据帧有效性
 * @param frame_data 数据帧指针
 * @return true-数据有效, false-数据无效
 */
bool tfmini_validate_frame(const tfmini_frame_data_t *frame_data);

/**
 * @brief 转换TFmini距离值为厘米
 * @param raw_distance 原始距离值
 * @param strength 信号强度
 * @return 距离值(cm)，异常时返回0
 */
uint16_t tfmini_convert_distance_cm(uint16_t raw_distance, uint16_t strength);

/**
 * @brief 转换TFmini温度值为摄氏度
 * @param raw_temperature 原始温度值
 * @return 温度值(摄氏度)
 */
int16_t tfmini_convert_temperature_celsius(uint16_t raw_temperature);

/**
 * @brief 重置TFmini协议解析器状态
 * @param parser 解析器指针
 */
void tfmini_parser_reset(tfmini_parser_t *parser);

/*==============================================================================
 * TFmini核心API函数声明 - 简化接口设计
 *============================================================================*/

/**
 * @brief 初始化TFmini传感器系统
 */
void tofmini_init(void);


/**
 * @brief 获取TFmini距离值
 * @return 距离值(cm)，无效时返回0
 */
uint16_t tofmini_get_distance_cm(void);

/**
 * @brief 获取TFmini距离数据有效性
 * @return true-数据有效, false-数据无效
 */
bool tofmini_is_distance_valid(void);

/**
 * @brief TFmini状态检查函数
 * @param dT_s 时间间隔（秒），通常为0.001f（1ms）
 */
void tofmini_check_state(float dT_s);

/**
 * @brief TFmini字节接收处理函数
 * @param link_type 链路类型（兼容参数，实际忽略）
 * @param byte 接收的字节
 */
void TOFmini_RecvOneByte(uint8_t link_type, uint8_t byte);

/*==============================================================================
 * TFmini配置函数声明
 *============================================================================*/

/**
 * @brief 设置TFmini滤波算法
 * @param filter_algorithm 滤波算法类型
 * @param temporal_filter_points 时域滤波点数(1-8)
 */
void tofmini_set_filter(tfmini_filter_algorithm_t filter_algorithm, uint8_t temporal_filter_points);

/**
 * @brief 获取TFmini传感器状态信息
 * @return 传感器数据结构指针
 */
const tfmini_sensor_data_t* tofmini_get_sensor_info(void);

/*==============================================================================
 * TFmini内部函数声明
 *============================================================================*/

/**
 * @brief 处理TFmini数据帧
 * @param frame_data TFmini数据帧指针
 */
void tfmini_process_frame(const tfmini_frame_data_t *frame_data);

/**
 * @brief TFmini距离计算
 * @return 滤波后的距离(cm)
 */
uint16_t tfmini_calculate_distance(void);

/**
 * @brief TFmini时域滤波
 * @param new_value 新的距离值
 * @return 滤波后的距离值
 */
uint16_t tfmini_temporal_filter(uint16_t new_value);

extern  tfmini_sensor_data_t g_tfmini_sensor;   // 传感器数据

#endif // TOFMINI_H
