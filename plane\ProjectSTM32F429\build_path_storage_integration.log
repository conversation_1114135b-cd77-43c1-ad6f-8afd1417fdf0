*** Using Compiler 'V5.06 update 7 (build 960)', folder: 'D:\keil5\ARM\ARMCC\Bin'
Build target 'Ano_LX'
compiling path_storage.c...
..\FcSrc\User\path_storage.h(16): error:  #5: cannot open source input file "sys.h": No such file or directory
  #include "sys.h"
..\FcSrc\User\path_storage.c: 0 warnings, 1 error
compiling Path_Planner.c...
"no source": Error:  #5: cannot open source input file "..\FcSrc\User\Path_Planner.c": No such file or directory
..\FcSrc\User\Path_Planner.c: 0 warnings, 1 error
compiling Maixcam.c...
..\FcSrc\User\path_storage.h(16): error:  #5: cannot open source input file "sys.h": No such file or directory
  #include "sys.h"
..\FcSrc\User\Maixcam.c: 0 warnings, 1 error
compiling zigbee.c...
..\FcSrc\User\path_storage.h(16): error:  #5: cannot open source input file "sys.h": No such file or directory
  #include "sys.h"
..\FcSrc\User\zigbee.c: 0 warnings, 1 error
compiling PID.c...
..\FcSrc\User\path_storage.h(16): error:  #5: cannot open source input file "sys.h": No such file or directory
  #include "sys.h"
..\FcSrc\User\PID.c: 0 warnings, 1 error
compiling Drv_Uart.c...
..\FcSrc\User\path_storage.h(16): error:  #5: cannot open source input file "sys.h": No such file or directory
  #include "sys.h"
..\DriversMcu\STM32F4xx\Drivers\Drv_Uart.c: 0 warnings, 1 error
compiling User_Task.c...
..\FcSrc\User\path_storage.h(16): error:  #5: cannot open source input file "sys.h": No such file or directory
  #include "sys.h"
..\FcSrc\User_Task.c: 0 warnings, 1 error
".\build\ANO_LX.axf" - 7 Error(s), 0 Warning(s).
Target not created.
Build Time Elapsed:  00:00:02
