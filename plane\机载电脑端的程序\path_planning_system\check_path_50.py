#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查第50个路径的安全问题
"""

import json

def check_path_50():
    with open('optimized_return_paths.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    path_data = data['path_data']
    
    # 检查第50个路径（索引49）
    path_50 = path_data[49]
    
    print(f"第50个组合:")
    print(f"禁飞区: {path_50['no_fly_zones']}")
    print(f"巡查路径: {path_50['patrol_path_sequence']}")
    
    # 检查11→22的移动
    patrol_path = path_50['patrol_path_sequence']
    
    for i in range(len(patrol_path) - 1):
        current = patrol_path[i]
        next_pos = patrol_path[i + 1]
        
        if current == 11 and next_pos == 22:
            print(f"\n⚠️ 发现问题移动: {current} → {next_pos}")
            print(f"位置: 巡查路径第{i+1}步到第{i+2}步")
            
            # 分析这个移动的安全性
            # 11 = A1B1 (左上角)
            # 22 = A2B2 (右下一格)
            # 对角线移动会经过 (A1B2, A2B1) 区域
            
            no_fly_zones = path_50['no_fly_zones']
            print(f"禁飞区: {no_fly_zones}")
            
            # 检查对角线路径是否与禁飞区冲突
            # 从11到22的对角线会经过12和21的中间区域
            potential_conflicts = [12, 21]
            
            conflicts = []
            for zone in no_fly_zones:
                if zone in potential_conflicts:
                    conflicts.append(zone)
            
            if conflicts:
                print(f"❌ 安全冲突: 对角线移动会经过禁飞区附近 {conflicts}")
            else:
                print(f"✅ 无直接禁飞区冲突，但仍需考虑飞机尺寸")
            
            break
    else:
        print("\n✅ 未发现11→22的移动")

if __name__ == "__main__":
    check_path_50()
