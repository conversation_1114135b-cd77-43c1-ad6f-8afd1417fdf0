#include "Maixcam.h"
#include "Drv_Uart.h"
#include "Ano_Math.h"
#include "zigbee.h"        // 包含动物记录函数声明


// Maixcam协议全局变量
maixcam_info maixcam;
/*******************************************************
    函数名：maixcam_receiver_GetOneByte
    输  入: const u8 linktype - 链路类型
           const u8 data - 接收到的单字节数据
    输  出: 无
    功能说明：Maixcam协议信息接收解包
    协议格式：AA FF ID X_low X_high Y_low Y_high count EA (9字节固定长度)
    字段说明：
    - AA FF: 帧头 (2字节)
    - ID: 标识符 (1字节) - 0=普通坐标，1-5=动物类型(象、虎、狼、猴、孔雀)
    - X_low, X_high: X坐标的低字节和高字节 (2字节)
    - Y_low, Y_high: Y坐标的低字节和高字节 (2字节)
    - count: 动物数量 (1字节，当ID为1-5时有效)
    - EA: 帧尾 (1字节)
********************************************************/
void maixcam_receiver_GetOneByte(const u8 linktype, const u8 data)
{
    static u8 maixcam_flag = 0;        // case语句输入值
    static u8 maixcam_datalen = 0;     // 数据长度计数
    static u8 maixcam_receive_buf[6];  // Maixcam接收缓冲区(ID + X_low + X_high + Y_low + Y_high + count = 6字节)

    switch(maixcam_flag)
    {
        case 0: // 等待帧头0xAA
        {
            if(data == 0xAA)
                maixcam_flag++;
            maixcam_datalen = 0;
        }
        break;

        case 1: // 等待帧头0xFF
        {
            if(data == 0xFF)
                maixcam_flag++;
            else
                maixcam_flag = 0;
        }
        break;

        case 2: // 接收数据 (ID + X_low + X_high + Y_low + Y_high)
        {
            maixcam_receive_buf[maixcam_datalen++] = data;

            if(maixcam_datalen == 6) // 接收完6字节数据
            {
                maixcam_flag++;
            }
        }
        break;

        case 3: // 等待帧尾0xEA
        {
            if(data == 0xEA) // 判断帧尾
            {
                // 解析数据
                maixcam.id = maixcam_receive_buf[0]; // 标识符

                // 使用联合体处理字节序转换
                coord_union x_coord, y_coord;

                // 合并X坐标 (低字节在前，高字节在后)
                x_coord.bytes.low = maixcam_receive_buf[1];   // X_low
                x_coord.bytes.high = maixcam_receive_buf[2];  // X_high
                maixcam.x = x_coord.coord;

                // 合并Y坐标 (低字节在前，高字节在后)
                y_coord.bytes.low = maixcam_receive_buf[3];   // Y_low
                y_coord.bytes.high = maixcam_receive_buf[4];  // Y_high
                maixcam.y = y_coord.coord;

                // 动物数量
                maixcam.count = maixcam_receive_buf[5]; // 动物数量

                // 设置数据有效标志
                maixcam.data_valid = 1;
                maixcam.last_update_ms = GetSysRunTimeMs(); // 更新时间戳


            }
            maixcam_flag = 0;
        }
        break;

        default:
            maixcam_flag = 0;
            break;
    }
}

/*******************************************************
    函数名：maixcam_send_data
    输  入: u8 id - 标识符 (0=普通坐标，1-5=动物类型)
           s16 x - X坐标
           s16 y - Y坐标
           u8 count - 动物数量
    输  出: 无
    功能说明：Maixcam数据发送函数
    协议格式：AA FF ID X_low X_high Y_low Y_high count EA (9字节固定长度)
********************************************************/
void maixcam_send_data(u8 id, s16 x, s16 y, u8 count)
{
    u8 send_buf[9];
    coord_union x_coord, y_coord;

    // 设置帧头
    send_buf[0] = 0xAA;
    send_buf[1] = 0xFF;

    // 设置标识符
    send_buf[2] = id;

    // 使用联合体处理X坐标的字节序转换
    x_coord.coord = x;
    send_buf[3] = x_coord.bytes.low;   // X_low
    send_buf[4] = x_coord.bytes.high;  // X_high

    // 使用联合体处理Y坐标的字节序转换
    y_coord.coord = y;
    send_buf[5] = y_coord.bytes.low;   // Y_low
    send_buf[6] = y_coord.bytes.high;  // Y_high

    // 设置动物数量
    send_buf[7] = count;

    // 设置帧尾
    send_buf[8] = 0xEA;

    // 发送数据包
    DrvUart2SendBuf(send_buf, 9);  // 使用UART2发送9字节数据
}

/*******************************************************
    函数名：maixcam_get_x
    输  入: 无
    输  出: s16 - X坐标值
    功能说明：获取Maixcam X坐标
********************************************************/
s16 maixcam_get_x(void)
{
    return maixcam.x;
}

/*******************************************************
    函数名：maixcam_get_y
    输  入: 无
    输  出: s16 - Y坐标值
    功能说明：获取Maixcam Y坐标
********************************************************/
s16 maixcam_get_y(void)
{
    return maixcam.y;
}

/*******************************************************
    函数名：maixcam_get_count
    输  入: 无
    输  出: u8 - 动物数量
    功能说明：获取识别到的动物数量
********************************************************/
u8 maixcam_get_count(void)
{
    return maixcam.count;
}

/*******************************************************
    函数名：maixcam_is_data_valid
    输  入: 无
    输  出: u8 - 数据有效标志 (1:有效, 0:无效)
    功能说明：检查Maixcam数据是否有效
********************************************************/
u8 maixcam_is_data_valid(void)
{
    return maixcam.data_valid;
}

/*******************************************************
    函数名：maixcam_get_id
    输  入: 无
    输  出: u8 - 动物类型ID
    功能说明：获取识别到的动物类型ID (1=象，2=虎，3=狼，4=猴，5=孔雀)
********************************************************/
u8 maixcam_get_id(void)
{
    return maixcam.id;
}

// maixcam_calculate_position_code函数已移除
// 原因：Maixcam传输的是屏幕像素坐标，不是物理位置坐标
// 无法准确将屏幕坐标转换为网格位置代码
