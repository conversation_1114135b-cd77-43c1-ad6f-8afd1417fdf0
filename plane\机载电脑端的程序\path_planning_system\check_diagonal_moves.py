#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对角线移动检查工具
检查path_storage.c中所有路径是否存在违反移动约束的对角线移动

版权：米醋电子工作室
创建时间：2025-07-31
编码格式：UTF-8
"""

import re
import sys
from typing import List, Tuple, Dict

def position_code_to_coordinates(pos_code: int) -> Tuple[int, int]:
    """
    将position_code转换为网格坐标
    
    Args:
        pos_code: 位置编码 (如91表示A9B1)
        
    Returns:
        (row, col): 网格坐标，row为A1-A9(1-9)，col为B1-B7(1-7)
    """
    row = pos_code // 10  # A1=1, A2=2, ..., A9=9
    col = pos_code % 10   # B1=1, B2=2, ..., B7=7
    return (row, col)

def is_diagonal_move(pos1: int, pos2: int) -> bool:
    """
    检查两个位置之间是否为对角线移动
    
    Args:
        pos1, pos2: 两个位置编码
        
    Returns:
        True if 对角线移动, False if 水平/垂直移动
    """
    row1, col1 = position_code_to_coordinates(pos1)
    row2, col2 = position_code_to_coordinates(pos2)
    
    row_diff = abs(row2 - row1)
    col_diff = abs(col2 - col1)
    
    # 对角线移动：既改变行又改变列
    return row_diff > 0 and col_diff > 0

def is_adjacent_move(pos1: int, pos2: int) -> bool:
    """
    检查两个位置是否为相邻移动（水平或垂直相邻）
    
    Args:
        pos1, pos2: 两个位置编码
        
    Returns:
        True if 相邻移动, False if 非相邻移动
    """
    row1, col1 = position_code_to_coordinates(pos1)
    row2, col2 = position_code_to_coordinates(pos2)
    
    row_diff = abs(row2 - row1)
    col_diff = abs(col2 - col1)
    
    # 相邻移动：只能是水平或垂直相邻，且距离为1
    return (row_diff == 1 and col_diff == 0) or (row_diff == 0 and col_diff == 1)

def parse_path_storage_file(file_path: str) -> List[Dict]:
    """
    解析path_storage.c文件，提取所有路径数据
    
    Args:
        file_path: path_storage.c文件路径
        
    Returns:
        路径数据列表
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"❌ 无法读取文件 {file_path}: {e}")
        return []
    
    paths = []
    
    # 匹配路径注释和数据
    path_pattern = r'// 路径(\d+): 禁飞区\[([^\]]+)\], 巡查长度(\d+), 返航长度(\d+)\s*\{[^}]+\{([^}]+)\}[^}]+\{([^}]+)\}'
    
    matches = re.findall(path_pattern, content, re.DOTALL)
    
    for match in matches:
        path_id = int(match[0])
        no_fly_zones_str = match[1]
        patrol_length = int(match[2])
        return_length = int(match[3])
        patrol_sequence_str = match[4]
        return_sequence_str = match[5]
        
        # 解析禁飞区
        no_fly_zones = [int(x.strip()) for x in no_fly_zones_str.split(',')]
        
        # 解析巡查路径序列
        patrol_numbers = re.findall(r'\b\d+\b', patrol_sequence_str)
        patrol_sequence = [int(x) for x in patrol_numbers if int(x) > 0]
        
        # 解析返航路径序列
        return_numbers = re.findall(r'\b\d+\b', return_sequence_str)
        return_sequence = [int(x) for x in return_numbers if int(x) > 0]
        
        paths.append({
            'id': path_id,
            'no_fly_zones': no_fly_zones,
            'patrol_length': patrol_length,
            'return_length': return_length,
            'patrol_sequence': patrol_sequence,
            'return_sequence': return_sequence
        })
    
    return paths

def check_path_for_diagonal_moves(path_data: Dict) -> List[Dict]:
    """
    检查单个路径中的对角线移动
    
    Args:
        path_data: 路径数据字典
        
    Returns:
        违规移动列表
    """
    violations = []
    path_id = path_data['id']
    
    # 检查巡查路径
    patrol_sequence = path_data['patrol_sequence']
    for i in range(len(patrol_sequence) - 1):
        pos1 = patrol_sequence[i]
        pos2 = patrol_sequence[i + 1]
        
        if is_diagonal_move(pos1, pos2):
            row1, col1 = position_code_to_coordinates(pos1)
            row2, col2 = position_code_to_coordinates(pos2)
            violations.append({
                'path_id': path_id,
                'type': '巡查路径',
                'step': i + 1,
                'from_pos': pos1,
                'to_pos': pos2,
                'from_coord': f'A{row1}B{col1}',
                'to_coord': f'A{row2}B{col2}',
                'description': f'对角线移动：{pos1}({row1},{col1}) → {pos2}({row2},{col2})'
            })
    
    # 检查返航路径
    return_sequence = path_data['return_sequence']
    for i in range(len(return_sequence) - 1):
        pos1 = return_sequence[i]
        pos2 = return_sequence[i + 1]
        
        if is_diagonal_move(pos1, pos2):
            row1, col1 = position_code_to_coordinates(pos1)
            row2, col2 = position_code_to_coordinates(pos2)
            violations.append({
                'path_id': path_id,
                'type': '返航路径',
                'step': i + 1,
                'from_pos': pos1,
                'to_pos': pos2,
                'from_coord': f'A{row1}B{col1}',
                'to_coord': f'A{row2}B{col2}',
                'description': f'对角线移动：{pos1}({row1},{col1}) → {pos2}({row2},{col2})'
            })
    
    return violations

def main():
    """主函数"""
    print("🔍 路径规划系统对角线移动检查工具")
    print("版权：米醋电子工作室")
    print("=" * 60)
    
    # 文件路径
    file_path = "../../FcSrc/User/path_storage.c"
    
    # 解析路径数据
    print("📖 正在解析path_storage.c文件...")
    paths = parse_path_storage_file(file_path)
    
    if not paths:
        print("❌ 未能解析到路径数据")
        return
    
    print(f"✅ 成功解析 {len(paths)} 条路径")
    print()
    
    # 检查所有路径
    all_violations = []
    
    for path_data in paths:
        violations = check_path_for_diagonal_moves(path_data)
        all_violations.extend(violations)
    
    # 输出结果
    if all_violations:
        print(f"❌ 发现 {len(all_violations)} 个对角线移动违规:")
        print("=" * 60)
        
        for violation in all_violations:
            print(f"🚨 路径{violation['path_id']} - {violation['type']} - 步骤{violation['step']}")
            print(f"   {violation['description']}")
            print(f"   坐标: {violation['from_coord']} → {violation['to_coord']}")
            print()
    else:
        print("✅ 所有路径都符合移动约束规则，未发现对角线移动违规")
    
    print("=" * 60)
    print(f"📊 检查完成: {len(paths)} 条路径, {len(all_violations)} 个违规")

if __name__ == "__main__":
    main()
