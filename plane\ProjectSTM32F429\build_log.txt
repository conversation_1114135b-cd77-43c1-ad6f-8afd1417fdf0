*** Using Compiler 'V5.06 update 7 (build 960)', folder: 'D:\keil5\ARM\ARMCC\Bin'
Build target 'Ano_LX'
compiling User_Task.c...
..\FcSrc\User_Task.c(1178): error:  #20: identifier "ANOLOGCOLOR_YELLOW" is undefined
                      AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_YELLOW,
..\FcSrc\User_Task.c(1312): warning:  #177-D: variable "ch8_value"  was declared but never referenced
  	uint16_t ch8_value = rc_in.rc_ch.st_data.ch_[ch_8_aux4];
..\FcSrc\User_Task.c(1622): error:  #20: identifier "ANOLOGCOLOR_CYAN" is undefined
              AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_CYAN, debug_str);
..\FcSrc\User_Task.c(1630): error:  #101: "current_time" has already been declared in the current scope
          uint32_t current_time = GetSysRunTimeMs();
..\FcSrc\User_Task.c(1675): warning:  #177-D: function "calculate_descent_progress"  was declared but never referenced
  static float calculate_descent_progress(void)
..\FcSrc\User_Task.c(154): warning:  #177-D: variable "dadian_f"  was declared but never referenced
  static u8 dadian_f = 0;
..\FcSrc\User_Task.c(161): warning:  #177-D: variable "patrol_points_completed"  was declared but never referenced
  static int patrol_points_completed = 0;        // 已完成巡查点数量
..\FcSrc\User_Task.c(175): warning:  #550-D: variable "descent_angle"  was set but never used
  static float descent_angle = 45.0f;            // 降落角度
..\FcSrc\User_Task.c(210): warning:  #550-D: variable "current_patrol_step"  was set but never used
  static int current_patrol_step = 0;            // 当前巡查步骤索引
..\FcSrc\User_Task.c(340): warning:  #177-D: function "is_dadian_down_command"  was declared but never referenced
  static inline bool is_dadian_down_command(uint16_t ch_value) {
..\FcSrc\User_Task.c(364): warning:  #177-D: function "is_CAM_reached"  was declared but never referenced
  static inline bool is_CAM_reached(void) {
..\FcSrc\User_Task.c(571): warning:  #177-D: function "handle_patrol_navigation"  was declared but never referenced
  static void handle_patrol_navigation(int patrol_step)
..\FcSrc\User_Task.c: 9 warnings, 3 errors
".\build\ANO_LX.axf" - 3 Error(s), 9 Warning(s).
Target not created.
Build Time Elapsed:  00:00:02
