#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预计算路径生成器 - 基于Dijkstra算法
版权信息：米醋电子工作室
创建日期：2025-07-30
编码格式：UTF-8

功能描述：
生成所有可能的禁飞区组合，使用Dijkstra算法预计算最优路径，
为单片机提供高性能的路径查表数据。

核心特性：
1. 智能组合生成：水平和垂直3连续禁飞区组合
2. 最优路径计算：使用已验证的Dijkstra算法
3. 数据验证：确保路径完整性和最优性
4. C代码生成：自动生成单片机可用的数据结构
"""

import json
import time
from typing import List, Tuple, Dict, Set
from algorithms.dijkstra import DijkstraPlanner
from core.grid_map import GridMap
from core.path_result import PathResult

class NoFlyZoneCombinationGenerator:
    """禁飞区组合生成器"""
    
    def __init__(self):
        """初始化生成器"""
        self.grid_rows = 7  # B1-B7
        self.grid_cols = 9  # A1-A9
        self.start_position_code = 91  # A9B1起点
        
    def generate_horizontal_combinations(self) -> List[List[int]]:
        """
        生成水平3连续禁飞区组合
        
        返回:
            List[List[int]]: 水平禁飞区组合列表
        """
        combinations = []
        
        # 遍历每一行（B1-B7）
        for row in range(1, self.grid_rows + 1):
            # 每行可以有7种起始位置（A1-A7开始的3连续）
            for start_col in range(1, self.grid_cols - 1):  # A1-A7
                combination = []
                for i in range(3):  # 3个连续点
                    col = start_col + i
                    position_code = col * 10 + row
                    combination.append(position_code)
                combinations.append(combination)
        
        return combinations
    
    def generate_vertical_combinations(self) -> List[List[int]]:
        """
        生成垂直3连续禁飞区组合
        
        返回:
            List[List[int]]: 垂直禁飞区组合列表
        """
        combinations = []
        
        # 遍历每一列（A1-A9）
        for col in range(1, self.grid_cols + 1):
            # 每列可以有5种起始位置（B1-B5开始的3连续）
            for start_row in range(1, self.grid_rows - 1):  # B1-B5
                combination = []
                for i in range(3):  # 3个连续点
                    row = start_row + i
                    position_code = col * 10 + row
                    combination.append(position_code)
                combinations.append(combination)
        
        return combinations
    
    def filter_invalid_combinations(self, combinations: List[List[int]]) -> List[List[int]]:
        """
        过滤包含A9B1起点的无效组合
        
        参数:
            combinations: 原始组合列表
            
        返回:
            List[List[int]]: 有效组合列表
        """
        valid_combinations = []
        
        for combination in combinations:
            if self.start_position_code not in combination:
                valid_combinations.append(combination)
        
        return valid_combinations
    
    def generate_all_combinations(self) -> List[List[int]]:
        """
        生成所有有效的禁飞区组合
        
        返回:
            List[List[int]]: 所有有效禁飞区组合
        """
        print("🔄 生成禁飞区组合...")
        
        # 生成水平组合
        horizontal = self.generate_horizontal_combinations()
        print(f"   水平组合: {len(horizontal)}种")
        
        # 生成垂直组合
        vertical = self.generate_vertical_combinations()
        print(f"   垂直组合: {len(vertical)}种")
        
        # 合并所有组合
        all_combinations = horizontal + vertical
        print(f"   总组合: {len(all_combinations)}种")
        
        # 过滤无效组合
        valid_combinations = self.filter_invalid_combinations(all_combinations)
        print(f"   有效组合: {len(valid_combinations)}种")
        
        return valid_combinations

class PathPrecomputer:
    """路径预计算器"""
    
    def __init__(self):
        """初始化预计算器"""
        self.planner = DijkstraPlanner()
        self.grid_map = GridMap()
        self.start_grid_pos = (0, 8)  # A9B1对应的网格坐标：B1=row0, A9=col8
        
    def compute_path_for_combination(self, no_fly_zones: List[int]) -> Dict:
        """
        为指定禁飞区组合计算最优路径和返航路径

        参数:
            no_fly_zones: 禁飞区position_code列表

        返回:
            Dict: 路径计算结果（包含巡查路径和返航路径）
        """
        # 创建新的网格地图并设置禁飞区
        grid_map = GridMap()
        grid_map.set_no_fly_zones(no_fly_zones)

        # 执行巡查路径规划
        patrol_result = self.planner.plan_path(grid_map, self.start_grid_pos)

        # 计算返航路径
        return_path = []
        return_length = 0
        if patrol_result.is_valid() and len(patrol_result.path_sequence) > 0:
            # 获取巡查路径的最后一个点
            last_position_code = patrol_result.path_sequence[-1]
            last_grid_pos = self._position_code_to_grid(last_position_code)

            # 计算从最后点到起点A9B1的返航路径
            return_path = self.planner.calculate_return_path(grid_map, last_grid_pos, self.start_grid_pos)
            return_length = len(return_path)

        # 计算覆盖率
        expected_points = 63 - len(no_fly_zones)  # 总点数减去禁飞区
        actual_points = len(patrol_result.path_sequence)
        coverage_rate = (actual_points / expected_points * 100) if expected_points > 0 else 0

        # 返回结果（确保JSON可序列化）
        return {
            'no_fly_zones': [int(x) for x in no_fly_zones],
            'path_sequence': [int(x) for x in patrol_result.path_sequence],
            'path_length': int(len(patrol_result.path_sequence)),
            'return_path': [int(x) for x in return_path],
            'return_length': int(return_length),
            'computation_time_ms': float(patrol_result.statistics.computation_time_ms),
            'total_distance': float(patrol_result.statistics.total_length),
            'coverage_rate': float(coverage_rate),
            'is_valid': bool(patrol_result.is_valid())
        }

    def _position_code_to_grid(self, position_code: int) -> Tuple[int, int]:
        """
        将position_code转换为网格坐标

        参数:
            position_code: 位置编码（如91表示A9B1）

        返回:
            Tuple[int, int]: 网格坐标(row, col)
        """
        # position_code格式：AB，其中A是行(1-9)，B是列(1-7)
        row_num = position_code // 10  # A部分：1-9
        col_num = position_code % 10   # B部分：1-7

        # 飞机坐标系：X正方向B1→B7，Y正方向A9→A1
        # 网格坐标：row=B-1(0-6)，col=9-A(0-8)
        grid_row = col_num - 1  # B1->0, B7->6 (X轴)
        grid_col = 9 - row_num  # A9->0, A1->8 (Y轴，注意方向)

        return (grid_row, grid_col)
    
    def precompute_all_paths(self, combinations: List[List[int]]) -> List[Dict]:
        """
        预计算所有组合的最优路径
        
        参数:
            combinations: 禁飞区组合列表
            
        返回:
            List[Dict]: 所有路径计算结果
        """
        print(f"🚀 开始预计算 {len(combinations)} 种禁飞区组合的最优路径...")
        
        results = []
        start_time = time.time()
        
        for i, combination in enumerate(combinations):
            print(f"   计算进度: {i+1}/{len(combinations)} - 禁飞区: {combination}")
            
            try:
                result = self.compute_path_for_combination(combination)
                results.append(result)
                
                # 显示计算结果
                if result['is_valid']:
                    print(f"     ✅ 巡查路径: {result['path_length']} 点, "
                          f"返航路径: {result['return_length']} 点, "
                          f"距离: {result['total_distance']:.1f}, "
                          f"覆盖率: {result['coverage_rate']:.1f}%, "
                          f"计算时间: {result['computation_time_ms']:.2f}ms")
                else:
                    print(f"     ❌ 路径计算失败")
                    
            except Exception as e:
                print(f"     ❌ 计算异常: {e}")
                # 添加失败记录
                results.append({
                    'no_fly_zones': combination,
                    'path_sequence': [],
                    'path_length': 0,
                    'return_path': [],
                    'return_length': 0,
                    'computation_time_ms': 0,
                    'total_distance': 0,
                    'coverage_rate': 0,
                    'is_valid': False,
                    'error': str(e)
                })
        
        total_time = time.time() - start_time
        print(f"🎉 预计算完成! 总耗时: {total_time:.2f}秒")
        
        return results

class PathDataValidator:
    """路径数据验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.expected_start_code = 91  # A9B1
        
    def validate_path_data(self, results: List[Dict]) -> Dict:
        """
        验证路径数据的完整性和正确性
        
        参数:
            results: 路径计算结果列表
            
        返回:
            Dict: 验证报告
        """
        print("🔍 验证路径数据...")
        
        total_combinations = len(results)
        valid_paths = 0
        invalid_paths = 0
        start_point_errors = 0
        coverage_errors = 0
        
        for result in results:
            if not result['is_valid']:
                invalid_paths += 1
                continue
                
            # 检查起点
            path_sequence = result['path_sequence']
            if not path_sequence or path_sequence[0] != self.expected_start_code:
                start_point_errors += 1
                continue
                
            # 检查覆盖率
            expected_points = 63 - 3  # 总点数减去禁飞区
            if result['path_length'] < expected_points * 0.95:  # 允许5%误差
                coverage_errors += 1
                continue
                
            valid_paths += 1
        
        validation_report = {
            'total_combinations': total_combinations,
            'valid_paths': valid_paths,
            'invalid_paths': invalid_paths,
            'start_point_errors': start_point_errors,
            'coverage_errors': coverage_errors,
            'success_rate': (valid_paths / total_combinations) * 100 if total_combinations > 0 else 0
        }
        
        print(f"   总组合数: {total_combinations}")
        print(f"   有效路径: {valid_paths}")
        print(f"   无效路径: {invalid_paths}")
        print(f"   起点错误: {start_point_errors}")
        print(f"   覆盖错误: {coverage_errors}")
        print(f"   成功率: {validation_report['success_rate']:.1f}%")
        
        return validation_report

def main():
    """主函数"""
    print("🚀 PC端禁飞区组合分析与路径预计算")
    print("=" * 70)
    
    # 1. 生成禁飞区组合
    generator = NoFlyZoneCombinationGenerator()
    combinations = generator.generate_all_combinations()
    
    # 2. 预计算路径
    precomputer = PathPrecomputer()
    results = precomputer.precompute_all_paths(combinations)
    
    # 3. 验证数据
    validator = PathDataValidator()
    validation_report = validator.validate_path_data(results)
    
    # 4. 保存结果
    output_file = "precomputed_paths_data.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump({
            'metadata': {
                'generation_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                'total_combinations': len(combinations),
                'validation_report': validation_report
            },
            'path_data': results
        }, f, indent=2, ensure_ascii=False)
    
    print(f"📁 数据已保存到: {output_file}")
    print("=" * 70)
    print("🎉 任务完成!")

if __name__ == "__main__":
    main()
