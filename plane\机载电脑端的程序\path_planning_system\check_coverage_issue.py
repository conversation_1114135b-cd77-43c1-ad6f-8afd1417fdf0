#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查路径覆盖率问题
"""

import json

def get_all_valid_positions():
    """获取所有有效位置（7x9网格）"""
    valid_positions = []
    for col in range(1, 10):  # A1到A9
        for row in range(1, 8):  # B1到B7
            position_code = col * 10 + row
            valid_positions.append(position_code)
    return sorted(valid_positions)

def check_path_coverage():
    """检查路径覆盖率问题"""
    
    # 加载安全修正后的数据
    with open('safe_optimized_return_paths.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    path_data = data['path_data']
    all_valid_positions = get_all_valid_positions()
    
    print(f"所有有效位置 ({len(all_valid_positions)}个):")
    print(all_valid_positions)
    print()
    
    # 检查路径50 (索引49)
    path_50 = path_data[49]
    no_fly_zones = path_50['no_fly_zones']
    patrol_path = path_50['patrol_path_sequence']
    
    print(f"路径50分析:")
    print(f"禁飞区: {no_fly_zones}")
    print(f"巡查路径长度: {len(patrol_path)}")
    print(f"巡查路径: {patrol_path}")
    print()
    
    # 计算应该巡查的位置（排除禁飞区）
    should_patrol = [pos for pos in all_valid_positions if pos not in no_fly_zones]
    print(f"应该巡查的位置 ({len(should_patrol)}个):")
    print(should_patrol)
    print()
    
    # 检查实际巡查的位置
    actual_patrol = set(patrol_path)
    should_patrol_set = set(should_patrol)
    
    print(f"实际巡查位置数: {len(actual_patrol)}")
    print(f"应该巡查位置数: {len(should_patrol_set)}")
    print()
    
    # 找出缺失的位置
    missing_positions = should_patrol_set - actual_patrol
    extra_positions = actual_patrol - should_patrol_set
    
    if missing_positions:
        print(f"❌ 缺失的位置: {sorted(missing_positions)}")
    else:
        print("✅ 没有缺失位置")
    
    if extra_positions:
        print(f"⚠️ 多余的位置: {sorted(extra_positions)}")
    else:
        print("✅ 没有多余位置")
    
    print()
    
    # 检查是否有重复访问
    duplicates = []
    seen = set()
    for pos in patrol_path:
        if pos in seen:
            duplicates.append(pos)
        seen.add(pos)
    
    if duplicates:
        print(f"⚠️ 重复访问的位置: {duplicates}")
    else:
        print("✅ 没有重复访问")
    
    # 计算真实覆盖率
    coverage_rate = len(actual_patrol & should_patrol_set) / len(should_patrol_set) * 100
    print(f"真实覆盖率: {coverage_rate:.1f}%")
    
    return missing_positions, should_patrol

def check_original_data():
    """检查原始数据是否有问题"""
    
    print("\n" + "="*50)
    print("检查原始预计算数据")
    print("="*50)
    
    # 检查原始数据
    try:
        with open('optimized_return_paths.json', 'r', encoding='utf-8') as f:
            original_data = json.load(f)
        
        path_50_original = original_data['path_data'][49]
        print(f"原始路径50巡查序列长度: {len(path_50_original['patrol_path_sequence'])}")
        print(f"原始路径50巡查序列: {path_50_original['patrol_path_sequence']}")
        
        # 检查原始数据的覆盖率
        all_valid = get_all_valid_positions()
        no_fly = path_50_original['no_fly_zones']
        should_patrol = [pos for pos in all_valid if pos not in no_fly]
        actual_patrol = set(path_50_original['patrol_path_sequence'])
        should_patrol_set = set(should_patrol)
        
        missing = should_patrol_set - actual_patrol
        if missing:
            print(f"❌ 原始数据也缺失位置: {sorted(missing)}")
        else:
            print("✅ 原始数据覆盖完整")
            
    except FileNotFoundError:
        print("原始数据文件不存在")

if __name__ == "__main__":
    missing, should_patrol = check_path_coverage()
    check_original_data()
    
    if missing:
        print(f"\n🚨 发现问题：路径50缺失 {len(missing)} 个位置")
        print("需要重新生成完整的巡查路径数据")
    else:
        print("\n✅ 路径50覆盖率正常")
