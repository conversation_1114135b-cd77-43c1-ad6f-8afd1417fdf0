#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
C代码数据一致性验证工具
版权信息：米醋电子工作室
创建日期：2025-07-31
作者：Alex (工程师)
编码格式：UTF-8

功能描述：
验证生成的C代码中的路径数据与JSON源数据是否完全一致
"""

import json
import re
from typing import List, Dict, Tuple

class CDataConsistencyVerifier:
    """C代码数据一致性验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.json_data = None
        self.c_data = None
        self.errors = []
        self.warnings = []
    
    def load_json_data(self, json_file: str) -> bool:
        """加载JSON数据"""
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            self.json_data = data['path_data']
            print(f"✅ 成功加载JSON数据: {len(self.json_data)} 个路径")
            return True
        except Exception as e:
            self.errors.append(f"JSON数据加载失败: {e}")
            return False
    
    def parse_c_data(self, c_file: str) -> bool:
        """解析C代码中的数据"""
        try:
            with open(c_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找路径数据数组
            pattern = r'static const precomputed_path_t path_lookup_table\[PRECOMPUTED_PATH_COUNT\] = \{(.*?)\};'
            match = re.search(pattern, content, re.DOTALL)
            
            if not match:
                self.errors.append("未找到路径数据数组")
                return False
            
            array_content = match.group(1)
            
            # 解析每个路径条目
            self.c_data = []
            
            # 查找所有路径条目
            entry_pattern = r'// 路径(\d+): 禁飞区\[(\d+), (\d+), (\d+)\], 巡查长度(\d+), 返航长度(\d+)\s*\{[^}]*\{(\d+), (\d+), (\d+)\},[^}]*\{([^}]+)\},[^}]*(\d+),[^}]*\{([^}]+)\}[^}]*\}'
            
            entries = re.findall(entry_pattern, array_content, re.DOTALL)
            
            for entry in entries:
                path_num = int(entry[0])
                no_fly_zones = [int(entry[1]), int(entry[2]), int(entry[3])]
                patrol_length = int(entry[4])
                return_length = int(entry[5])
                
                # 解析巡查路径
                patrol_data = entry[9].strip()
                patrol_numbers = re.findall(r'\d+', patrol_data)
                patrol_path = [int(x) for x in patrol_numbers if int(x) > 0]
                
                # 解析返航路径
                return_data = entry[11].strip()
                return_numbers = re.findall(r'\d+', return_data)
                return_path = [int(x) for x in return_numbers if int(x) > 0]
                
                self.c_data.append({
                    'path_number': path_num,
                    'no_fly_zones': no_fly_zones,
                    'patrol_length': patrol_length,
                    'patrol_path': patrol_path,
                    'return_length': return_length,
                    'return_path': return_path
                })
            
            print(f"✅ 成功解析C代码数据: {len(self.c_data)} 个路径")
            return True
            
        except Exception as e:
            self.errors.append(f"C代码解析失败: {e}")
            return False
    
    def verify_consistency(self) -> Dict:
        """验证数据一致性"""
        print("\n🔍 验证数据一致性...")
        
        if not self.json_data or not self.c_data:
            self.errors.append("数据未正确加载")
            return {'success': False, 'errors': self.errors}
        
        # 检查数量一致性
        if len(self.json_data) != len(self.c_data):
            self.errors.append(f"路径数量不一致: JSON={len(self.json_data)}, C={len(self.c_data)}")
        
        # 逐个验证路径数据
        consistent_count = 0
        
        for i in range(min(len(self.json_data), len(self.c_data))):
            json_path = self.json_data[i]
            c_path = self.c_data[i]
            
            path_errors = []
            
            # 验证禁飞区
            json_no_fly = json_path['no_fly_zones']
            c_no_fly = c_path['no_fly_zones']
            
            if json_no_fly != c_no_fly:
                path_errors.append(f"禁飞区不一致: JSON={json_no_fly}, C={c_no_fly}")
            
            # 验证巡查路径
            json_patrol = json_path['patrol_path_sequence']
            c_patrol = c_path['patrol_path']
            
            if json_patrol != c_patrol:
                path_errors.append(f"巡查路径不一致: 长度 JSON={len(json_patrol)}, C={len(c_patrol)}")
                
                # 详细比较前10个点
                for j in range(min(10, len(json_patrol), len(c_patrol))):
                    if json_patrol[j] != c_patrol[j]:
                        path_errors.append(f"巡查路径第{j+1}点不一致: JSON={json_patrol[j]}, C={c_patrol[j]}")
            
            # 验证返航路径
            json_return = json_path['return_path_sequence']
            c_return = c_path['return_path']
            
            if json_return != c_return:
                path_errors.append(f"返航路径不一致: 长度 JSON={len(json_return)}, C={len(c_return)}")
                
                # 详细比较
                for j in range(min(len(json_return), len(c_return))):
                    if json_return[j] != c_return[j]:
                        path_errors.append(f"返航路径第{j+1}点不一致: JSON={json_return[j]}, C={c_return[j]}")
            
            if path_errors:
                self.errors.extend([f"路径{i+1}: {error}" for error in path_errors])
            else:
                consistent_count += 1
            
            # 只显示前3个路径的详细信息
            if i < 3:
                print(f"   路径{i+1}: 禁飞区{json_no_fly} - {'✅一致' if not path_errors else '❌不一致'}")
        
        consistency_rate = (consistent_count / len(self.json_data)) * 100 if self.json_data else 0
        
        print(f"\n📊 一致性验证结果:")
        print(f"   一致路径数: {consistent_count}/{len(self.json_data)}")
        print(f"   一致性率: {consistency_rate:.1f}%")
        
        return {
            'success': len(self.errors) == 0,
            'consistent_count': consistent_count,
            'total_count': len(self.json_data) if self.json_data else 0,
            'consistency_rate': consistency_rate,
            'errors': self.errors,
            'warnings': self.warnings
        }
    
    def generate_report(self, result: Dict) -> str:
        """生成验证报告"""
        report = []
        report.append("# C代码数据一致性验证报告")
        report.append("=" * 50)
        report.append("")
        
        if result['success']:
            report.append("## ✅ 验证结果：成功")
            report.append(f"- 一致路径数: {result['consistent_count']}/{result['total_count']}")
            report.append(f"- 一致性率: {result['consistency_rate']:.1f}%")
            report.append("- C代码数据与JSON源数据完全一致")
        else:
            report.append("## ❌ 验证结果：失败")
            report.append(f"- 一致路径数: {result['consistent_count']}/{result['total_count']}")
            report.append(f"- 一致性率: {result['consistency_rate']:.1f}%")
            report.append("")
            report.append("### 发现的错误:")
            for error in result['errors']:
                report.append(f"- {error}")
        
        if result['warnings']:
            report.append("")
            report.append("### 警告:")
            for warning in result['warnings']:
                report.append(f"- {warning}")
        
        return "\n".join(report)

def main():
    """主函数"""
    print("🚀 C代码数据一致性验证工具")
    print("=" * 70)
    print("版权信息：米醋电子工作室")
    print("创建日期：2025-07-31")
    print("作者：Alex (工程师)")
    print("=" * 70)
    
    verifier = CDataConsistencyVerifier()
    
    # 加载数据
    json_success = verifier.load_json_data('optimized_return_paths.json')
    c_success = verifier.parse_c_data('../../FcSrc/User/path_storage.c')
    
    if not json_success or not c_success:
        print("❌ 数据加载失败")
        for error in verifier.errors:
            print(f"   {error}")
        return
    
    # 验证一致性
    result = verifier.verify_consistency()
    
    # 生成报告
    report = verifier.generate_report(result)
    print("\n" + report)
    
    # 保存报告
    with open('c_data_consistency_report.txt', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n📁 详细报告已保存: c_data_consistency_report.txt")
    
    # 总结
    if result['success']:
        print("\n🎉 数据一致性验证通过!")
        print("✅ C代码数据与JSON源数据完全一致")
        print("✅ 可以安全使用生成的C代码")
    else:
        print(f"\n❌ 发现 {len(result['errors'])} 个不一致问题")
        print("需要修复后才能使用")

if __name__ == "__main__":
    main()
