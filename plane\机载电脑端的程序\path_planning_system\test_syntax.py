#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语法验证测试
版权信息：米醋电子工作室
创建日期：2025-07-31

功能描述：
验证扩展后的代码语法正确性，不依赖numpy等外部库
"""

import ast
import os

def check_python_syntax(file_path):
    """检查Python文件语法"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            source = f.read()
        
        # 解析语法树
        ast.parse(source)
        return True, "语法正确"
    except SyntaxError as e:
        return False, f"语法错误: {e}"
    except Exception as e:
        return False, f"其他错误: {e}"

def main():
    """主测试函数"""
    print("🔍 代码语法验证测试")
    print("=" * 50)
    
    # 检查修改的文件
    files_to_check = [
        "algorithms/dijkstra.py",
        "precompute_paths.py"
    ]
    
    all_passed = True
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"📄 检查文件: {file_path}")
            passed, message = check_python_syntax(file_path)
            
            if passed:
                print(f"   ✅ {message}")
            else:
                print(f"   ❌ {message}")
                all_passed = False
        else:
            print(f"📄 文件不存在: {file_path}")
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有文件语法检查通过!")
    else:
        print("❌ 存在语法错误，请检查代码")

def test_position_code_conversion():
    """测试position_code转换逻辑"""
    print("\n🧮 测试position_code转换逻辑")
    print("=" * 50)
    
    def position_code_to_grid(position_code: int) -> tuple:
        """将position_code转换为网格坐标"""
        row_num = position_code // 10  # A部分：1-9
        col_num = position_code % 10   # B部分：1-7

        # 坐标系统：A1B7(左上) A1B1(左下) A9B7(右上) A9B1(右下起点)
        grid_row = col_num - 1  # B1->0, B7->6 (X轴：下到上)
        grid_col = 9 - row_num  # A9->0, A1->8 (Y轴：右到左)

        return (grid_row, grid_col)

    def grid_to_position_code(grid_row: int, grid_col: int) -> int:
        """将网格坐标转换为position_code"""
        row_num = 9 - grid_col  # A部分：1-9
        col_num = grid_row + 1  # B部分：1-7
        return row_num * 10 + col_num
    
    # 测试用例 - 根据实际转换结果验证
    test_cases = [
        (91, (0, 0), "A9B1 右下角起点"),
        (11, (0, 8), "A1B1 左下角"),
        (17, (6, 8), "A1B7 左上角"),
        (97, (6, 0), "A9B7 右上角"),
        (44, (3, 5), "A4B4 中心点"),
    ]
    
    print("📋 测试position_code转换:")
    for position_code, expected_grid, description in test_cases:
        actual_grid = position_code_to_grid(position_code)
        reverse_code = grid_to_position_code(*actual_grid)
        
        grid_correct = actual_grid == expected_grid
        reverse_correct = reverse_code == position_code
        
        status = "✅" if (grid_correct and reverse_correct) else "❌"
        print(f"   {status} {description}: {position_code} -> {actual_grid} -> {reverse_code}")
        
        if not grid_correct:
            print(f"      期望网格: {expected_grid}, 实际: {actual_grid}")
        if not reverse_correct:
            print(f"      反向转换错误: {reverse_code} != {position_code}")

def test_data_structure():
    """测试数据结构设计"""
    print("\n📊 测试数据结构设计")
    print("=" * 50)
    
    # 模拟扩展后的数据结构
    sample_result = {
        'no_fly_zones': [33, 34, 35],
        'path_sequence': [91, 81, 71, 61, 51, 41, 42, 32, 22, 12],  # 示例巡查路径
        'path_length': 10,
        'return_path': [12, 22, 32, 42, 52, 62, 72, 82, 91],  # 示例返航路径
        'return_length': 9,
        'computation_time_ms': 15.5,
        'total_distance': 125.8,
        'coverage_rate': 95.2,
        'is_valid': True
    }
    
    print("📋 扩展后的数据结构示例:")
    for key, value in sample_result.items():
        print(f"   {key}: {value}")
    
    # 验证数据完整性
    required_fields = [
        'no_fly_zones', 'path_sequence', 'path_length',
        'return_path', 'return_length', 'computation_time_ms',
        'total_distance', 'coverage_rate', 'is_valid'
    ]
    
    missing_fields = [field for field in required_fields if field not in sample_result]
    
    if not missing_fields:
        print("   ✅ 数据结构完整")
    else:
        print(f"   ❌ 缺少字段: {missing_fields}")
    
    # 验证返航路径逻辑
    if sample_result['return_path']:
        start_point = sample_result['path_sequence'][-1] if sample_result['path_sequence'] else None
        end_point = sample_result['return_path'][-1] if sample_result['return_path'] else None
        
        print(f"   📍 巡查终点: {start_point}")
        print(f"   🏠 返航终点: {end_point}")
        
        if end_point == 91:
            print("   ✅ 返航终点正确 (A9B1)")
        else:
            print(f"   ❌ 返航终点错误: 期望91, 实际{end_point}")

if __name__ == "__main__":
    main()
    test_position_code_conversion()
    test_data_structure()
