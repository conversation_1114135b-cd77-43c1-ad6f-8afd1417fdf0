*** Using Compiler 'V5.06 update 7 (build 960)', folder: 'D:\keil5\ARM\ARMCC\Bin'
Build target 'Ano_LX'
compiling PID.c...
..\FcSrc\User\tofmini.h(276): warning:  #1-D: last line of file ends without a newline
  #endif // TOFMINI_H
..\FcSrc\User\PID.c: 1 warning, 0 errors
linking...
Program Size: Code=77900 RO-data=3016 RW-data=2784 ZI-data=20096  
FromELF: creating hex file...
After Build - User command #1: fromelf.exe --bin -o ./ANO-LX.bin ./build/Ano_LX.axf
".\build\ANO_LX.axf" - 0 Error(s), 1 Warning(s).
Build Time Elapsed:  00:00:02
