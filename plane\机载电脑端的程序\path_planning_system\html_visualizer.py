#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML可视化生成器
版权信息：米醋电子工作室
创建日期：2025-07-31
作者：Emma (产品经理)
编码格式：UTF-8

功能描述：
开发HTMLVisualizer类，生成HTML表格格式的路径可视化。
显示禁飞区（红色标记）、巡查路径（蓝色箭头，按顺序编号）、返航路径（绿色箭头）、起点/终点（特殊标记）。
"""

import json
import time
from typing import List, Dict, Tuple

class HTMLVisualizer:
    """HTML可视化生成器类"""
    
    def __init__(self):
        """初始化可视化器"""
        self.grid_rows = 7  # B1-B7
        self.grid_cols = 9  # A1-A9
        
    def position_code_to_grid(self, position_code: int) -> Tuple[int, int]:
        """position_code转网格坐标"""
        if position_code < 11 or position_code > 97:
            return (-1, -1)
        
        col_num = position_code // 10  # A列号 (1-9)
        row_num = position_code % 10   # B行号 (1-7)
        
        if col_num < 1 or col_num > 9 or row_num < 1 or row_num > 7:
            return (-1, -1)
        
        row = row_num - 1  # B1->0, B2->1, ..., B7->6
        col = col_num - 1  # A1->0, A2->1, ..., A9->8
        
        return (row, col)
    
    def grid_to_position_code(self, row: int, col: int) -> int:
        """网格坐标转position_code"""
        return (col + 1) * 10 + (row + 1)
    
    def generate_css_styles(self) -> str:
        """生成CSS样式"""
        return """
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                margin: 20px;
                background-color: #f5f5f5;
            }
            
            .container {
                max-width: 1200px;
                margin: 0 auto;
                background-color: white;
                padding: 20px;
                border-radius: 10px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }
            
            h1 {
                text-align: center;
                color: #2c3e50;
                margin-bottom: 30px;
                font-size: 28px;
            }
            
            h2 {
                color: #34495e;
                border-bottom: 2px solid #3498db;
                padding-bottom: 10px;
                margin-top: 30px;
            }
            
            .grid-container {
                margin: 20px 0;
                overflow-x: auto;
            }
            
            .path-grid {
                border-collapse: collapse;
                margin: 20px auto;
                font-family: 'Courier New', monospace;
                font-size: 12px;
                background-color: white;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
            
            .path-grid th, .path-grid td {
                width: 40px;
                height: 40px;
                text-align: center;
                vertical-align: middle;
                border: 1px solid #bdc3c7;
                position: relative;
                font-weight: bold;
            }
            
            .path-grid th {
                background-color: #ecf0f1;
                color: #2c3e50;
                font-size: 11px;
            }
            
            /* 网格单元格样式 */
            .cell-empty {
                background-color: #ffffff;
                color: #95a5a6;
            }
            
            .cell-no-fly {
                background-color: #e74c3c;
                color: white;
                font-weight: bold;
            }
            
            .cell-start {
                background-color: #f39c12;
                color: white;
                font-weight: bold;
                font-size: 14px;
            }
            
            .cell-patrol {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                font-size: 10px;
            }
            
            .cell-return {
                background-color: #27ae60;
                color: white;
                font-weight: bold;
                font-size: 10px;
            }
            
            .cell-end {
                background-color: #9b59b6;
                color: white;
                font-weight: bold;
                font-size: 14px;
            }
            
            /* 箭头样式 */
            .arrow {
                position: absolute;
                font-size: 16px;
                font-weight: bold;
            }
            
            .arrow-right { right: 2px; top: 50%; transform: translateY(-50%); }
            .arrow-left { left: 2px; top: 50%; transform: translateY(-50%); }
            .arrow-down { bottom: 2px; left: 50%; transform: translateX(-50%); }
            .arrow-up { top: 2px; left: 50%; transform: translateX(-50%); }
            .arrow-dr { bottom: 2px; right: 2px; }
            .arrow-dl { bottom: 2px; left: 2px; }
            .arrow-ur { top: 2px; right: 2px; }
            .arrow-ul { top: 2px; left: 2px; }
            
            /* 图例样式 */
            .legend {
                display: flex;
                flex-wrap: wrap;
                justify-content: center;
                gap: 20px;
                margin: 20px 0;
                padding: 15px;
                background-color: #f8f9fa;
                border-radius: 8px;
            }
            
            .legend-item {
                display: flex;
                align-items: center;
                gap: 8px;
            }
            
            .legend-color {
                width: 20px;
                height: 20px;
                border-radius: 3px;
                border: 1px solid #bdc3c7;
            }
            
            /* 统计信息样式 */
            .stats {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
                margin: 20px 0;
            }
            
            .stat-item {
                background-color: #f8f9fa;
                padding: 15px;
                border-radius: 8px;
                text-align: center;
                border-left: 4px solid #3498db;
            }
            
            .stat-value {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
            }
            
            .stat-label {
                font-size: 14px;
                color: #7f8c8d;
                margin-top: 5px;
            }
            
            /* 响应式设计 */
            @media (max-width: 768px) {
                .path-grid th, .path-grid td {
                    width: 30px;
                    height: 30px;
                    font-size: 10px;
                }
                
                .legend {
                    flex-direction: column;
                    align-items: center;
                }
            }
        </style>
        """
    
    def generate_legend(self) -> str:
        """生成图例"""
        return """
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #f39c12;"></div>
                <span>起点 (A9B1)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #e74c3c;"></div>
                <span>禁飞区</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #3498db;"></div>
                <span>巡查路径</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #27ae60;"></div>
                <span>返航路径</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #9b59b6;"></div>
                <span>终点 (返回起点)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #ffffff; border: 1px solid #bdc3c7;"></div>
                <span>未访问区域</span>
            </div>
        </div>
        """
    
    def calculate_arrow_direction(self, from_pos: int, to_pos: int) -> str:
        """计算箭头方向"""
        from_row, from_col = self.position_code_to_grid(from_pos)
        to_row, to_col = self.position_code_to_grid(to_pos)
        
        if from_row == -1 or to_row == -1:
            return ""
        
        dr = to_row - from_row
        dc = to_col - from_col
        
        # 8方向箭头映射
        direction_map = {
            (-1, -1): "↖", (-1, 0): "↑", (-1, 1): "↗",
            (0, -1): "←",  (0, 0): "",   (0, 1): "→",
            (1, -1): "↙",  (1, 0): "↓",  (1, 1): "↘"
        }
        
        return direction_map.get((dr, dc), "")
    
    def generate_grid_html(self, path_data: Dict) -> str:
        """生成网格HTML表格"""
        no_fly_zones = set(path_data['no_fly_zones'])
        patrol_path = path_data['patrol_path_sequence']
        return_path = path_data['return_path_sequence']

        # 创建网格数据结构
        grid_data = {}

        # 标记禁飞区
        for pos in no_fly_zones:
            grid_data[pos] = {'type': 'no_fly', 'content': '✖', 'order': -1}

        # 标记巡查路径 - 显示完整的60个点
        for i, pos in enumerate(patrol_path):
            if pos not in no_fly_zones:
                if i == 0:
                    grid_data[pos] = {'type': 'start', 'content': 'S', 'order': 0}
                else:
                    # 显示完整的顺序编号
                    if i <= 99:
                        content = str(i)
                    else:
                        content = '*'
                    grid_data[pos] = {'type': 'patrol', 'content': content, 'order': i}

        # 标记返航路径 - 确保正确显示返航路径
        patrol_positions = set(patrol_path)
        for i, pos in enumerate(return_path):
            if pos not in no_fly_zones:
                # 如果返航路径的点不在巡查路径中，或者是起点/终点，则标记为返航
                if pos not in patrol_positions or pos == 91:  # A9B1起点特殊处理
                    if pos == 91 and i == len(return_path) - 1:  # 最后一个点是起点，标记为终点
                        grid_data[pos] = {'type': 'end', 'content': 'E', 'order': len(patrol_path) + i}
                    else:
                        content = f"R{i+1}" if i < 9 else "R*"
                        grid_data[pos] = {'type': 'return', 'content': content, 'order': len(patrol_path) + i}
        
        # 生成HTML表格 - 修正坐标系统显示
        html = '<div class="grid-container">\n'
        html += '<table class="path-grid">\n'

        # 表头 - A1到A9从左到右
        html += '<tr><th></th>'
        for col in range(self.grid_cols):
            html += f'<th>A{col+1}</th>'
        html += '<th></th></tr>\n'

        # 表格行 - B7到B1从上到下（Y轴从下到上的显示）
        for display_row in range(self.grid_rows):
            actual_row = self.grid_rows - 1 - display_row  # B7=0, B6=1, ..., B1=6
            row_label = f"B{actual_row + 1}"
            html += f'<tr><th>{row_label}</th>'

            for col in range(self.grid_cols):
                pos_code = self.grid_to_position_code(actual_row, col)
                cell_data = grid_data.get(pos_code, {'type': 'empty', 'content': '·', 'order': -1})

                cell_class = f"cell-{cell_data['type']}"
                content = cell_data['content']

                html += f'<td class="{cell_class}">{content}</td>'

            html += f'<th>{row_label}</th></tr>\n'

        # 表尾 - A1到A9从左到右
        html += '<tr><th></th>'
        for col in range(self.grid_cols):
            html += f'<th>A{col+1}</th>'
        html += '<th></th></tr>\n'

        html += '</table>\n'
        html += '</div>\n'

        return html
    
    def generate_stats_html(self, path_data: Dict) -> str:
        """生成统计信息HTML"""
        stats = [
            ("禁飞区数量", len(path_data['no_fly_zones'])),
            ("巡查路径长度", path_data['patrol_path_length']),
            ("返航路径长度", path_data['return_path_length']),
            ("总飞行时间", f"{path_data['total_flight_time']:.1f}秒"),
            ("覆盖率", f"{path_data['coverage_rate']:.1f}%"),
            ("计算时间", f"{path_data['computation_time_ms']:.2f}ms")
        ]
        
        html = '<div class="stats">\n'
        for label, value in stats:
            html += f'''
            <div class="stat-item">
                <div class="stat-value">{value}</div>
                <div class="stat-label">{label}</div>
            </div>
            '''
        html += '</div>\n'
        
        return html
    
    def generate_single_path_html(self, path_data: Dict, index: int) -> str:
        """生成单个路径的HTML页面"""
        no_fly_zones_str = ', '.join(map(str, path_data['no_fly_zones']))
        
        html = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>无人机路径规划可视化 - 组合 {index + 1}</title>
            {self.generate_css_styles()}
        </head>
        <body>
            <div class="container">
                <h1>无人机路径规划可视化</h1>
                <h2>禁飞区组合 {index + 1}: [{no_fly_zones_str}]</h2>
                
                {self.generate_legend()}
                
                {self.generate_grid_html(path_data)}
                
                {self.generate_stats_html(path_data)}
                
                <h2>路径详情</h2>
                <div style="background-color: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0;">
                    <p><strong>巡查路径序列:</strong></p>
                    <p style="font-family: monospace; word-break: break-all; line-height: 1.6;">
                        {' → '.join(map(str, path_data['patrol_path_sequence'][:30]))}
                        {'...' if len(path_data['patrol_path_sequence']) > 30 else ''}
                    </p>
                    
                    <p><strong>返航路径序列:</strong></p>
                    <p style="font-family: monospace; word-break: break-all; line-height: 1.6;">
                        {' → '.join(map(str, path_data['return_path_sequence']))}
                    </p>
                </div>
                
                <div style="text-align: center; margin-top: 30px; color: #7f8c8d; font-size: 12px;">
                    <p>版权信息：米醋电子工作室 | 生成时间：{time.strftime('%Y-%m-%d %H:%M:%S')}</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return html
    
    def generate_index_html(self, all_path_data: List[Dict]) -> str:
        """生成索引页面HTML"""
        html = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>无人机路径规划可视化 - 总览</title>
            {self.generate_css_styles()}
        </head>
        <body>
            <div class="container">
                <h1>无人机路径规划可视化总览</h1>
                
                <div class="stats">
                    <div class="stat-item">
                        <div class="stat-value">{len(all_path_data)}</div>
                        <div class="stat-label">总路径组合数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{sum(1 for p in all_path_data if p['is_valid'])}</div>
                        <div class="stat-label">有效路径数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{sum(p['patrol_path_length'] for p in all_path_data) // len(all_path_data)}</div>
                        <div class="stat-label">平均巡查长度</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{sum(p['total_flight_time'] for p in all_path_data) / len(all_path_data):.1f}秒</div>
                        <div class="stat-label">平均飞行时间</div>
                    </div>
                </div>
                
                <h2>路径组合列表</h2>
                <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px; margin: 20px 0;">
        """
        
        for i, path_data in enumerate(all_path_data):
            no_fly_zones_str = ', '.join(map(str, path_data['no_fly_zones']))
            status_color = "#27ae60" if path_data['is_valid'] else "#e74c3c"
            
            html += f"""
                    <div style="border: 1px solid #bdc3c7; border-radius: 8px; padding: 15px; background-color: white;">
                        <h3 style="margin-top: 0; color: #2c3e50;">组合 {i + 1}</h3>
                        <p><strong>禁飞区:</strong> [{no_fly_zones_str}]</p>
                        <p><strong>状态:</strong> <span style="color: {status_color};">{'✓ 有效' if path_data['is_valid'] else '✗ 无效'}</span></p>
                        <p><strong>巡查长度:</strong> {path_data['patrol_path_length']} 点</p>
                        <p><strong>返航长度:</strong> {path_data['return_path_length']} 点</p>
                        <p><strong>总时间:</strong> {path_data['total_flight_time']:.1f} 秒</p>
                        <a href="path_{i+1:03d}.html" style="display: inline-block; background-color: #3498db; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin-top: 10px;">查看详情</a>
                    </div>
            """
        
        html += f"""
                </div>
                
                <div style="text-align: center; margin-top: 30px; color: #7f8c8d; font-size: 12px;">
                    <p>版权信息：米醋电子工作室 | 生成时间：{time.strftime('%Y-%m-%d %H:%M:%S')}</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return html

def main():
    """主函数 - 生成HTML可视化"""
    print("🚀 HTML可视化生成器")
    print("=" * 70)
    print("版权信息：米醋电子工作室")
    print("创建日期：2025-07-31")
    print("作者：Emma (产品经理)")
    print("=" * 70)
    print()
    
    # 加载路径数据
    try:
        with open('optimized_return_paths.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        path_data_list = data['path_data']
        print(f"📁 成功加载 {len(path_data_list)} 个路径数据")
    except FileNotFoundError:
        print("❌ 未找到优化后的路径数据文件")
        return
    
    # 创建可视化器
    visualizer = HTMLVisualizer()
    
    print("🎨 生成HTML可视化文件...")
    
    # 生成索引页面
    index_html = visualizer.generate_index_html(path_data_list)
    with open('visualization_index.html', 'w', encoding='utf-8') as f:
        f.write(index_html)
    print("   ✅ 生成索引页面: visualization_index.html")
    
    # 生成每个路径的详细页面
    generated_count = 0
    for i, path_data in enumerate(path_data_list):
        if path_data['is_valid']:
            filename = f"path_{i+1:03d}.html"
            path_html = visualizer.generate_single_path_html(path_data, i)
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(path_html)
            generated_count += 1
            
            if (i + 1) % 10 == 0:
                print(f"   📄 已生成 {i + 1}/{len(path_data_list)} 个页面")
    
    print(f"\n🎉 HTML可视化生成完成!")
    print(f"✅ 生成文件数: {generated_count + 1} 个")
    print(f"✅ 索引页面: visualization_index.html")
    print(f"✅ 详细页面: path_001.html ~ path_{len(path_data_list):03d}.html")
    print(f"✅ 有效页面: {generated_count} 个")
    
    print("\n📝 关键特性:")
    print("✅ 响应式设计，支持移动设备")
    print("✅ 颜色标记：红色禁飞区，蓝色巡查路径，绿色返航路径")
    print("✅ 路径顺序编号，清晰显示访问顺序")
    print("✅ 详细统计信息和路径序列")
    print("✅ 美观的CSS样式和图例说明")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
