#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
禁飞区组合生成与验证工具
版权信息：米醋电子工作室
创建日期：2025-07-31
作者：Emma (产品经理)
编码格式：UTF-8

功能描述：
生成所有可能的3个连续禁飞区组合，验证组合有效性，确保符合用户需求
"""

from typing import List, Tuple, Dict
import json

class NoFlyZoneCombinationGenerator:
    """禁飞区组合生成器 - 基于现有代码优化"""
    
    def __init__(self):
        """初始化生成器"""
        self.grid_rows = 7  # B1-B7
        self.grid_cols = 9  # A1-A9
        self.start_position_code = 91  # A9B1起点
        
    def generate_horizontal_combinations(self) -> List[List[int]]:
        """
        生成水平3连续禁飞区组合
        
        返回:
            List[List[int]]: 水平禁飞区组合列表
        """
        combinations = []
        
        # 遍历每一行（B1-B7）
        for row in range(1, self.grid_rows + 1):
            # 每行可以有7种起始位置（A1-A7开始的3连续）
            for start_col in range(1, self.grid_cols - 1):  # A1-A7
                combination = []
                for i in range(3):  # 3个连续点
                    col = start_col + i
                    position_code = col * 10 + row
                    combination.append(position_code)
                combinations.append(combination)
        
        return combinations
    
    def generate_vertical_combinations(self) -> List[List[int]]:
        """
        生成垂直3连续禁飞区组合
        
        返回:
            List[List[int]]: 垂直禁飞区组合列表
        """
        combinations = []
        
        # 遍历每一列（A1-A9）
        for col in range(1, self.grid_cols + 1):
            # 每列可以有5种起始位置（B1-B5开始的3连续）
            for start_row in range(1, self.grid_rows - 1):  # B1-B5
                combination = []
                for i in range(3):  # 3个连续点
                    row = start_row + i
                    position_code = col * 10 + row
                    combination.append(position_code)
                combinations.append(combination)
        
        return combinations
    
    def filter_invalid_combinations(self, combinations: List[List[int]]) -> List[List[int]]:
        """
        过滤包含A9B1起点的无效组合
        
        参数:
            combinations: 原始组合列表
            
        返回:
            List[List[int]]: 有效组合列表
        """
        valid_combinations = []
        
        for combination in combinations:
            if self.start_position_code not in combination:
                valid_combinations.append(combination)
        
        return valid_combinations
    
    def generate_all_combinations(self) -> List[List[int]]:
        """
        生成所有有效的禁飞区组合
        
        返回:
            List[List[int]]: 所有有效禁飞区组合
        """
        print("🔄 生成禁飞区组合...")
        
        # 生成水平组合
        horizontal = self.generate_horizontal_combinations()
        print(f"   水平组合: {len(horizontal)}种")
        
        # 生成垂直组合
        vertical = self.generate_vertical_combinations()
        print(f"   垂直组合: {len(vertical)}种")
        
        # 合并所有组合
        all_combinations = horizontal + vertical
        print(f"   总组合: {len(all_combinations)}种")
        
        # 过滤无效组合
        valid_combinations = self.filter_invalid_combinations(all_combinations)
        print(f"   有效组合: {len(valid_combinations)}种")
        
        return valid_combinations

class NoFlyZoneValidator:
    """禁飞区组合验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.start_position_code = 91  # A9B1起点
        
    def validate_continuous_zones(self, no_fly_zones: List[int]) -> bool:
        """
        验证禁飞区连续性（必须是3个连续的点）
        
        参数:
            no_fly_zones: 禁飞区position_code列表
            
        返回:
            bool: 是否连续
        """
        if len(no_fly_zones) != 3:
            return False
            
        # 转换为坐标
        coords = []
        for position_code in no_fly_zones:
            col_num = position_code // 10  # A列号 (1-9)
            row_num = position_code % 10   # B行号 (1-7)
            
            if col_num < 1 or col_num > 9 or row_num < 1 or row_num > 7:
                return False
            coords.append((row_num, col_num))
        
        # 检查是否在同一行或同一列
        rows = [coord[0] for coord in coords]
        cols = [coord[1] for coord in coords]
        
        if len(set(rows)) == 1:  # 同一行（水平连续）
            cols.sort()
            return cols[1] - cols[0] == 1 and cols[2] - cols[1] == 1
        elif len(set(cols)) == 1:  # 同一列（垂直连续）
            rows.sort()
            return rows[1] - rows[0] == 1 and rows[2] - rows[1] == 1
        
        return False
    
    def validate_no_start_point(self, no_fly_zones: List[int]) -> bool:
        """
        验证禁飞区不包含起点A9B1
        
        参数:
            no_fly_zones: 禁飞区position_code列表
            
        返回:
            bool: 是否不包含起点
        """
        return self.start_position_code not in no_fly_zones
    
    def validate_position_codes(self, no_fly_zones: List[int]) -> bool:
        """
        验证position_code有效性
        
        参数:
            no_fly_zones: 禁飞区position_code列表
            
        返回:
            bool: 是否所有position_code都有效
        """
        for position_code in no_fly_zones:
            col_num = position_code // 10  # A列号 (1-9)
            row_num = position_code % 10   # B行号 (1-7)
            
            if col_num < 1 or col_num > 9 or row_num < 1 or row_num > 7:
                return False
                
        return True
    
    def validate_combination(self, no_fly_zones: List[int]) -> Dict[str, bool]:
        """
        全面验证禁飞区组合
        
        参数:
            no_fly_zones: 禁飞区position_code列表
            
        返回:
            Dict[str, bool]: 验证结果详情
        """
        return {
            'count_valid': len(no_fly_zones) == 3,
            'continuous': self.validate_continuous_zones(no_fly_zones),
            'no_start_point': self.validate_no_start_point(no_fly_zones),
            'position_codes_valid': self.validate_position_codes(no_fly_zones)
        }
    
    def is_valid_combination(self, no_fly_zones: List[int]) -> bool:
        """
        判断禁飞区组合是否完全有效
        
        参数:
            no_fly_zones: 禁飞区position_code列表
            
        返回:
            bool: 是否完全有效
        """
        validation = self.validate_combination(no_fly_zones)
        return all(validation.values())

def analyze_combinations_statistics(combinations: List[List[int]]) -> Dict:
    """分析禁飞区组合统计信息"""
    
    # 按类型分类
    horizontal_count = 0
    vertical_count = 0
    
    for combo in combinations:
        # 检查是否为水平连续
        rows = [code % 10 for code in combo]
        cols = [code // 10 for code in combo]
        
        if len(set(rows)) == 1:  # 同一行
            horizontal_count += 1
        elif len(set(cols)) == 1:  # 同一列
            vertical_count += 1
    
    # 计算理论最大值
    # 水平：7行 × 7种起始位置 = 49种
    # 垂直：9列 × 5种起始位置 = 45种
    theoretical_horizontal = 7 * 7  # 49
    theoretical_vertical = 9 * 5    # 45
    theoretical_total = theoretical_horizontal + theoretical_vertical  # 94
    
    return {
        'total_combinations': len(combinations),
        'horizontal_combinations': horizontal_count,
        'vertical_combinations': vertical_count,
        'theoretical_maximum': theoretical_total,
        'coverage_rate': len(combinations) / theoretical_total * 100
    }

def generate_combination_examples():
    """生成禁飞区组合示例"""
    examples = {
        'horizontal_examples': [
            {
                'combination': [11, 21, 31],
                'description': 'A1B1-A2B1-A3B1 (第一行左侧)',
                'visualization': 'B1: [X][X][X][ ][ ][ ][ ][ ][ ]'
            },
            {
                'combination': [71, 81, 91],
                'description': 'A7B1-A8B1-A9B1 (第一行右侧，包含起点)',
                'note': '此组合无效，包含起点A9B1'
            },
            {
                'combination': [44, 54, 64],
                'description': 'A4B4-A5B4-A6B4 (中间行)',
                'visualization': 'B4: [ ][ ][ ][X][X][X][ ][ ][ ]'
            }
        ],
        'vertical_examples': [
            {
                'combination': [11, 12, 13],
                'description': 'A1B1-A1B2-A1B3 (第一列下方)',
                'visualization': 'A1列: B1[X] B2[X] B3[X] B4[ ] ...'
            },
            {
                'combination': [95, 96, 97],
                'description': 'A9B5-A9B6-A9B7 (第九列上方)',
                'visualization': 'A9列: ... B5[X] B6[X] B7[X]'
            },
            {
                'combination': [53, 54, 55],
                'description': 'A5B3-A5B4-A5B5 (中间列)',
                'visualization': 'A5列: ... B3[X] B4[X] B5[X] ...'
            }
        ]
    }
    return examples

def main():
    """主函数"""
    print("🚀 禁飞区组合生成与验证")
    print("=" * 70)
    print("版权信息：米醋电子工作室")
    print("创建日期：2025-07-31")
    print("作者：Emma (产品经理)")
    print("=" * 70)
    print()
    
    # 1. 生成禁飞区组合
    generator = NoFlyZoneCombinationGenerator()
    combinations = generator.generate_all_combinations()
    print()
    
    # 2. 验证组合
    validator = NoFlyZoneValidator()
    
    print("🔍 验证禁飞区组合...")
    valid_count = 0
    invalid_combinations = []
    
    for i, combo in enumerate(combinations):
        if validator.is_valid_combination(combo):
            valid_count += 1
        else:
            validation_details = validator.validate_combination(combo)
            invalid_combinations.append({
                'combination': combo,
                'validation': validation_details
            })
    
    print(f"   验证完成: {valid_count}/{len(combinations)} 个组合有效")
    
    if invalid_combinations:
        print(f"   发现 {len(invalid_combinations)} 个无效组合:")
        for invalid in invalid_combinations[:5]:  # 只显示前5个
            print(f"     {invalid['combination']} - {invalid['validation']}")
    print()
    
    # 3. 统计分析
    stats = analyze_combinations_statistics(combinations)
    print("📊 组合统计分析:")
    print(f"   总组合数: {stats['total_combinations']}")
    print(f"   水平组合: {stats['horizontal_combinations']}")
    print(f"   垂直组合: {stats['vertical_combinations']}")
    print(f"   理论最大值: {stats['theoretical_maximum']}")
    print(f"   覆盖率: {stats['coverage_rate']:.1f}%")
    print()
    
    # 4. 生成示例
    examples = generate_combination_examples()
    print("📋 组合示例:")
    print("水平组合示例:")
    for example in examples['horizontal_examples']:
        print(f"   {example['combination']} - {example['description']}")
        if 'note' in example:
            print(f"     注意: {example['note']}")
    print()
    
    print("垂直组合示例:")
    for example in examples['vertical_examples']:
        print(f"   {example['combination']} - {example['description']}")
    print()
    
    # 5. 保存结果
    result_data = {
        'metadata': {
            'generation_time': '2025-07-31',
            'total_combinations': len(combinations),
            'valid_combinations': valid_count,
            'statistics': stats
        },
        'combinations': combinations,
        'examples': examples
    }
    
    output_file = "no_fly_zone_combinations.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(result_data, f, indent=2, ensure_ascii=False)
    
    print(f"📁 结果已保存到: {output_file}")
    print()
    
    # 6. 总结
    print("🎉 禁飞区组合生成与验证完成!")
    print(f"✅ 成功生成 {len(combinations)} 个有效禁飞区组合")
    print(f"✅ 所有组合都是3个连续点")
    print(f"✅ 所有组合都不包含起点A9B1")
    print(f"✅ 覆盖了水平和垂直两种排列方式")
    print("📝 下一步: 使用这些组合进行路径预计算")
    print("=" * 70)

if __name__ == "__main__":
    main()
