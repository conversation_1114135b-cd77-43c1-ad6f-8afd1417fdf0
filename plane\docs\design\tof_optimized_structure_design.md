# TOF传感器优化数据结构设计
**版权：米醋电子工作室**  
**设计日期：2024年**  
**目标：STM32F429内存和性能优化**

## 🎯 设计目标

1. **内存优化**：减少90%以上的内存占用
2. **性能提升**：优化CPU处理效率
3. **功能保持**：保留核心测距功能
4. **兼容性**：保持API接口不变

## 📊 优化设计方案

### 方案1：极致轻量级结构（推荐）

```c
/**
 * @brief 优化的TOF传感器数据结构 - 极致轻量级版本
 * @note 内存占用：18字节/传感器（原413字节的4.4%）
 */
typedef struct {
    // 基础信息 (4字节)
    uint8_t sensor_id;              // 传感器ID (0-4)
    uint8_t filter_type;            // 滤波算法类型
    uint8_t filter_index;           // 滤波索引 (0-7)
    uint8_t filter_size;            // 滤波窗口大小 (3,4,8)
    
    // 测距结果 (4字节)
    uint16_t distance_cm;           // 滤波后距离 (0-400cm)
    uint8_t data_quality;           // 数据质量 (0-3)
    uint8_t valid_pixel_count;      // 有效像素数量 (0-64)
    
    // 状态信息 (2字节)
    uint8_t avg_signal_strength;    // 平均信号强度 (0-255)
    uint8_t status_flags;           // 状态标志位
                                    // bit0: is_distance_valid
                                    // bit1: pixel_mode (0=4x4, 1=8x8)
                                    // bit2-7: 保留
    
    // 滤波缓冲区 (8字节)
    uint16_t filter_buffer[4];      // 时域滤波缓冲区
    
} tof_sensor_optimized_t;           // 总计：18字节
```

### 方案2：平衡优化结构

```c
/**
 * @brief 平衡优化的TOF传感器数据结构
 * @note 内存占用：32字节/传感器（原413字节的7.7%）
 */
typedef struct {
    // 基础信息 (8字节)
    uint8_t sensor_id;              // 传感器ID
    uint8_t pixel_mode;             // 像素模式 (4x4/8x8)
    uint8_t filter_type;            // 滤波算法类型
    uint8_t filter_size;            // 滤波窗口大小
    uint8_t filter_index;           // 滤波索引
    uint8_t valid_pixel_count;      // 有效像素数量
    uint8_t data_quality;           // 数据质量等级
    uint8_t reserved;               // 对齐保留
    
    // 测距结果 (8字节)
    uint16_t distance_cm;           // 滤波后距离
    uint16_t avg_signal_strength;   // 平均信号强度 (保持精度)
    uint32_t status_flags;          // 扩展状态信息
                                    // bit0: is_distance_valid
                                    // bit1-31: 保留扩展
    
    // 滤波缓冲区 (16字节)
    uint16_t filter_buffer[8];      // 完整时域滤波缓冲区
    
} tof_sensor_balanced_t;            // 总计：32字节
```

### 方案3：兼容性优化结构

```c
/**
 * @brief 兼容性优化的TOF传感器数据结构
 * @note 内存占用：64字节/传感器（原413字节的15.5%）
 */
typedef struct {
    // 基础信息 (8字节)
    uint8_t sensor_id;
    tof_pixel_mode_t pixel_mode;
    uint8_t current_pixel_count;
    uint8_t valid_pixel_count;
    uint8_t data_quality;
    uint8_t filter_type;
    uint8_t filter_index;
    uint8_t filter_size;
    
    // 测距结果 (8字节)
    uint16_t distance_cm;
    uint16_t avg_signal_strength;
    uint32_t status_flags;
    
    // 压缩像素数据 (32字节)
    uint16_t valid_distances[16];   // 仅存储有效像素距离
    
    // 滤波缓冲区 (16字节)
    uint16_t filter_buffer[8];
    
} tof_sensor_compatible_t;          // 总计：64字节
```

## 🔧 实现策略

### 1. 渐进式迁移策略

```c
// 阶段1：定义新结构体，保持旧结构体兼容
typedef tof_sensor_optimized_t tof_sensor_new_t;
extern tof_sensor_new_t tof_sensors_new[TOF_MAX_SENSORS];

// 阶段2：提供转换函数
void tof_convert_to_optimized(const tof_sensor_t* old, tof_sensor_new_t* new);
void tof_convert_from_optimized(const tof_sensor_new_t* new, tof_sensor_t* old);

// 阶段3：逐步替换API实现
uint16_t tof_get_distance_cm_optimized(uint8_t sensor_id);
bool tof_is_distance_valid_optimized(uint8_t sensor_id);
```

### 2. 状态标志位设计

```c
// 状态标志位定义
#define TOF_STATUS_DISTANCE_VALID   (1 << 0)  // 距离数据有效
#define TOF_STATUS_PIXEL_MODE_8x8   (1 << 1)  // 像素模式：1=8x8, 0=4x4
#define TOF_STATUS_HIGH_QUALITY     (1 << 2)  // 高质量数据
#define TOF_STATUS_FILTER_READY     (1 << 3)  // 滤波器就绪
#define TOF_STATUS_SENSOR_ACTIVE    (1 << 4)  // 传感器激活
// bit5-7: 保留扩展

// 状态操作宏
#define TOF_SET_STATUS(sensor, flag)    ((sensor)->status_flags |= (flag))
#define TOF_CLEAR_STATUS(sensor, flag)  ((sensor)->status_flags &= ~(flag))
#define TOF_CHECK_STATUS(sensor, flag)  ((sensor)->status_flags & (flag))
```

### 3. 内存对齐优化

```c
// 确保结构体对齐到4字节边界，优化访问性能
#pragma pack(push, 4)
typedef struct {
    // 结构体定义
} tof_sensor_optimized_t;
#pragma pack(pop)

// 或使用编译器属性
typedef struct __attribute__((packed, aligned(4))) {
    // 结构体定义
} tof_sensor_optimized_t;
```

## 📈 性能对比分析

### 内存使用对比

| 方案 | 单传感器 | 5传感器 | 节省率 | 适用场景 |
|------|----------|---------|--------|----------|
| 原始结构 | 413字节 | 2065字节 | - | 完整功能 |
| 极致轻量 | 18字节 | 90字节 | 95.6% | 定高应用 |
| 平衡优化 | 32字节 | 160字节 | 92.2% | 通用应用 |
| 兼容优化 | 64字节 | 320字节 | 84.5% | 渐进迁移 |

### CPU性能影响

1. **缓存命中率提升**：更小的数据结构提高L1缓存利用率
2. **内存访问减少**：减少不必要的数据加载
3. **处理时间优化**：简化的数据结构减少处理开销

### 实时性能提升

```c
// 原始结构处理时间估算
// 内存拷贝：413字节 ≈ 100个CPU周期
// 数据处理：64像素处理 ≈ 500个CPU周期
// 总计：≈ 600个CPU周期

// 优化结构处理时间估算
// 内存拷贝：18字节 ≈ 5个CPU周期
// 数据处理：简化处理 ≈ 50个CPU周期
// 总计：≈ 55个CPU周期

// 性能提升：(600-55)/600 = 90.8%
```

## 🎯 推荐实施方案

### 推荐：方案1（极致轻量级结构）

**选择理由**：
1. **最大内存节省**：95.6%的内存优化，从2065字节降至90字节
2. **最佳实时性能**：最小化内存访问和处理开销
3. **适合STM32F429**：显著减少RAM压力
4. **满足定高需求**：保留核心测距功能

**实施计划**：
1. **第一阶段**：定义新数据结构，保持API兼容
2. **第二阶段**：逐步迁移内部实现
3. **第三阶段**：性能测试和优化验证
4. **第四阶段**：完全替换旧结构

### 关键设计决策

1. **像素数据处理**：不存储原始像素数据，直接计算最终结果
2. **滤波缓冲区**：压缩至4点，满足大部分滤波需求
3. **数据类型优化**：使用最小必要的数据类型
4. **状态压缩**：使用位标志压缩状态信息

### 风险缓解

1. **功能验证**：确保优化后功能完整性
2. **性能测试**：验证实际性能提升效果
3. **兼容性保证**：保持API接口不变
4. **渐进迁移**：分阶段实施，降低风险

## 📊 详细内存节省计算

### 当前内存使用详细计算

```c
// tof_pixel_data_t 结构体大小计算
sizeof(uint16_t) = 2  // distance_cm
sizeof(uint8_t)  = 1  // status
sizeof(uint16_t) = 2  // signal_strength
sizeof(bool)     = 1  // is_valid
// 总计：6字节/像素，无内存对齐填充

// tof_sensor_t 结构体大小计算
sizeof(uint8_t)  = 1  // sensor_id
sizeof(uint8_t)  = 1  // pixel_mode (枚举)
sizeof(tof_pixel_data_t) * 64 = 6 * 64 = 384  // pixels数组
sizeof(uint8_t)  = 1  // current_pixel_count
sizeof(uint8_t)  = 1  // valid_pixel_count
sizeof(uint16_t) = 2  // distance_cm
sizeof(bool)     = 1  // is_distance_valid
sizeof(uint16_t) = 2  // avg_signal_strength
sizeof(uint8_t)  = 1  // data_quality
sizeof(uint8_t)  = 1  // filter_type (枚举)
sizeof(uint16_t) * 8 = 16  // filter_buffer
sizeof(uint8_t)  = 1  // filter_index
sizeof(uint8_t)  = 1  // filter_size
// 总计：413字节/传感器
```

### 优化后内存使用计算

```c
// tof_sensor_optimized_t 结构体大小计算
sizeof(uint8_t)  = 1  // sensor_id
sizeof(uint8_t)  = 1  // filter_type
sizeof(uint8_t)  = 1  // filter_index
sizeof(uint8_t)  = 1  // filter_size
sizeof(uint16_t) = 2  // distance_cm
sizeof(uint8_t)  = 1  // data_quality
sizeof(uint8_t)  = 1  // valid_pixel_count
sizeof(uint8_t)  = 1  // avg_signal_strength
sizeof(uint8_t)  = 1  // status_flags
sizeof(uint16_t) * 4 = 8  // filter_buffer
// 总计：18字节/传感器
```

### 全局数组内存对比

```c
// 当前实现
tof_sensor_t tof_sensors[5];
// 内存占用：413字节 × 5 = 2065字节

// 优化实现
tof_sensor_optimized_t tof_sensors_opt[5];
// 内存占用：18字节 × 5 = 90字节

// 内存节省：2065 - 90 = 1975字节
// 节省率：1975 / 2065 = 95.64%
```

### STM32F429 RAM影响分析

```c
// STM32F429 RAM规格
#define STM32F429_TOTAL_RAM (192 * 1024)  // 192KB

// 当前TOF占用率
float current_usage = (2065.0 / STM32F429_TOTAL_RAM) * 100;
// = 1.05%

// 优化后TOF占用率
float optimized_usage = (90.0 / STM32F429_TOTAL_RAM) * 100;
// = 0.046%

// RAM释放量：1975字节 ≈ 1.93KB
// 可用于其他功能的额外RAM空间
```

## 🚀 实施代码示例

### 1. 新数据结构定义

```c
// 在tofsense-m.h中添加优化结构体
typedef struct {
    uint8_t sensor_id;
    uint8_t filter_type;
    uint8_t filter_index;
    uint8_t filter_size;
    uint16_t distance_cm;
    uint8_t data_quality;
    uint8_t valid_pixel_count;
    uint8_t avg_signal_strength;
    uint8_t status_flags;
    uint16_t filter_buffer[4];
} tof_sensor_optimized_t;

// 状态标志位定义
#define TOF_OPT_DISTANCE_VALID  0x01
#define TOF_OPT_PIXEL_MODE_8X8  0x02
#define TOF_OPT_HIGH_QUALITY    0x04
#define TOF_OPT_FILTER_READY    0x08
```

### 2. 转换函数实现

```c
// 从原始结构转换到优化结构
void tof_convert_to_optimized(const tof_sensor_t* src, tof_sensor_optimized_t* dst) {
    dst->sensor_id = src->sensor_id;
    dst->filter_type = src->filter_type;
    dst->filter_index = src->filter_index;
    dst->filter_size = src->filter_size;
    dst->distance_cm = src->distance_cm;
    dst->data_quality = src->data_quality;
    dst->valid_pixel_count = src->valid_pixel_count;

    // 压缩信号强度到8位
    dst->avg_signal_strength = (src->avg_signal_strength > 255) ?
                               255 : (uint8_t)src->avg_signal_strength;

    // 压缩状态标志
    dst->status_flags = 0;
    if (src->is_distance_valid) dst->status_flags |= TOF_OPT_DISTANCE_VALID;
    if (src->pixel_mode == TOF_MODE_8x8) dst->status_flags |= TOF_OPT_PIXEL_MODE_8X8;

    // 拷贝滤波缓冲区（前4个元素）
    for (int i = 0; i < 4; i++) {
        dst->filter_buffer[i] = src->filter_buffer[i];
    }
}
```

### 3. API兼容性保证

```c
// 保持原有API接口不变
uint16_t tof_get_distance_cm(uint8_t sensor_id) {
    if (sensor_id >= TOF_MAX_SENSORS) return 0;
    return tof_sensors_opt[sensor_id].distance_cm;
}

bool tof_is_distance_valid(uint8_t sensor_id) {
    if (sensor_id >= TOF_MAX_SENSORS) return false;
    return (tof_sensors_opt[sensor_id].status_flags & TOF_OPT_DISTANCE_VALID) != 0;
}
```
