#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试可视化输出
"""

import sys
import os
import importlib.util

# 强制重新加载GridMap模块
current_dir = os.path.dirname(os.path.abspath(__file__))

# 清除可能的缓存
if 'grid_map' in sys.modules:
    del sys.modules['grid_map']

# 重新导入
spec = importlib.util.spec_from_file_location("grid_map_new", os.path.join(current_dir, "core", "grid_map.py"))
grid_map_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(grid_map_module)
GridMap = grid_map_module.GridMap

def test_visualization():
    """测试可视化输出"""
    print("🔍 调试可视化输出")
    
    # 创建网格地图
    grid_map = GridMap()
    
    # 设置一个简单的路径
    path_sequence = [11, 21, 31, 41, 51]  # A1B1 -> A2B1 -> A3B1 -> A4B1 -> A5B1
    
    # 手动构建可视化来验证
    print("手动构建的正确可视化：")
    print("    A1 A2 A3 A4 A5 A6 A7 A8 A9")
    print("B1  S  1  2  3  E  .  .  .  .")
    print("B2  .  .  .  .  .  .  .  .  .")
    print("B3  .  .  .  .  .  .  .  .  .")
    print("B4  .  .  .  .  .  .  .  .  .")
    print("B5  .  .  .  .  .  .  .  .  .")
    print("B6  .  .  .  .  .  .  .  .  .")
    print("B7  .  .  .  .  .  .  .  .  .")
    print("    A1 A2 A3 A4 A5 A6 A7 A8 A9")

    print("\n系统生成的可视化：")
    # 显示可视化
    visualization = grid_map.visualize_path(path_sequence, "调试测试")
    print(visualization)
    
    # 验证坐标转换
    print("\n🔍 坐标转换验证:")
    test_codes = [11, 19, 71, 79, 97]
    for code in test_codes:
        row, col = grid_map.position_code_to_grid(code)
        back_code = grid_map.grid_to_position_code(row, col)
        print(f"Position {code} -> Grid ({row},{col}) -> Position {back_code}")

if __name__ == "__main__":
    test_visualization()
