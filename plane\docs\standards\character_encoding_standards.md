# 字符编码规范标准

## 规范概述
**适用范围**: ANO_LX_FC_PROv2.0飞控项目所有源代码文件  
**制定日期**: 2024年  
**版本**: v1.0  
**强制执行**: 是

## 1. 字符编码格式要求

### 1.1 统一编码标准
- **强制要求**: 所有源代码文件必须使用 **UTF-8 编码格式**
- **适用文件**: `.c`, `.h`, `.cpp`, `.hpp`, `.py`, `.js`, `.md` 等所有文本文件
- **BOM设置**: 不使用BOM (Byte Order Mark)
- **换行符**: 使用Unix风格换行符 (LF, `\n`)

### 1.2 编码格式验证
```bash
# 检查文件编码格式 (Windows PowerShell)
Get-Content -Path "文件路径" -Encoding UTF8

# 检查文件编码格式 (Linux/Mac)
file -bi "文件路径"
```

## 2. 中文注释编写要求

### 2.1 中文注释使用规范
- **版权信息**: 文件头部版权信息必须使用规范的中文UTF-8编码
- **函数注释**: 函数说明注释推荐使用中文，便于团队理解
- **行内注释**: 复杂逻辑的行内注释可使用中文说明
- **变量注释**: 重要变量的注释建议使用中文描述用途

### 2.2 中文注释格式标准
```c
/*==========================================================================
 * 版权    ：匿名科技开源飞控代码库
 * 创建时间：YYYY-MM-DD
 * 作者    ：作者姓名
 * 功能描述：文件主要功能说明
===========================================================================*/

// 函数功能说明 - 使用中文描述函数用途
static inline void Function_Name()
{
    static uint8_t variable = 0;    // 变量用途说明
    
    // 复杂逻辑的中文说明
    if (condition)
    {
        // 处理逻辑说明
    }
}
```

### 2.3 禁止使用的编码
- ❌ **GB2312**: 会导致乱码问题
- ❌ **GBK**: 兼容性差，不推荐
- ❌ **Big5**: 繁体中文编码，不适用
- ❌ **ASCII**: 不支持中文字符

## 3. 编码一致性操作步骤

### 3.1 新文件创建规范
1. **IDE设置**: 确保开发环境默认编码为UTF-8
2. **文件创建**: 新建文件时选择UTF-8编码
3. **模板使用**: 使用标准化的文件头模板
4. **编码验证**: 创建后立即验证编码格式

### 3.2 现有文件转换步骤
```powershell
# Step 1: 备份原文件
Copy-Item "原文件.c" "原文件.c.backup"

# Step 2: 转换编码 (使用PowerShell)
$content = Get-Content "原文件.c" -Encoding Default
$content | Out-File "原文件.c" -Encoding UTF8

# Step 3: 验证转换结果
Get-Content "原文件.c" -Encoding UTF8 | Select-Object -First 10
```

### 3.3 Keil μVision IDE配置
1. **打开Keil μVision**
2. **菜单**: Edit → Configuration
3. **Editor选项卡**: 
   - File Encoding: UTF-8
   - Default Encoding: UTF-8
4. **保存设置**: 点击OK保存配置

### 3.4 Visual Studio Code配置
```json
// settings.json
{
    "files.encoding": "utf8",
    "files.autoGuessEncoding": false,
    "files.eol": "\n"
}
```

## 4. 编码问题诊断与修复

### 4.1 常见乱码问题识别
```c
// 乱码示例 (GB2312/GBK编码显示)
����    �������ɿ����ô���������  // ❌ 乱码
����ʱ�䣺2020-02-06              // ❌ 乱码

// 正确显示 (UTF-8编码)
版权    ：匿名科技开源飞控代码库    // ✅ 正确
创建时间：2020-02-06              // ✅ 正确
```

### 4.2 乱码修复流程
1. **识别乱码**: 检查文件中的乱码位置
2. **确定原意**: 根据上下文推断原始中文含义
3. **逐行替换**: 使用str-replace-editor工具逐行修复
4. **编译验证**: 确保修复不影响代码功能
5. **格式检查**: 验证UTF-8编码格式正确

### 4.3 批量修复脚本
```powershell
# 批量检查项目中的编码问题
$files = Get-ChildItem -Path "FcSrc" -Filter "*.c" -Recurse
foreach ($file in $files) {
    $content = Get-Content $file.FullName -Raw -Encoding Default
    if ($content -match "[\u4e00-\u9fff]") {
        Write-Host "发现中文内容: $($file.FullName)"
        # 转换为UTF-8
        $content | Out-File $file.FullName -Encoding UTF8 -NoNewline
    }
}
```

## 5. 质量保证措施

### 5.1 编码检查清单
- [ ] 文件编码格式为UTF-8
- [ ] 中文注释显示正常
- [ ] 无BOM标记
- [ ] 换行符为LF格式
- [ ] 编译无错误
- [ ] 版权信息完整

### 5.2 自动化检查
```bash
# 编码格式检查脚本
#!/bin/bash
find . -name "*.c" -o -name "*.h" | while read file; do
    encoding=$(file -bi "$file" | cut -d'=' -f2)
    if [ "$encoding" != "utf-8" ]; then
        echo "编码错误: $file ($encoding)"
    fi
done
```

### 5.3 代码审查要求
- **提交前检查**: 每次代码提交前必须检查编码格式
- **审查重点**: 重点检查新增的中文注释
- **工具验证**: 使用工具自动验证编码一致性
- **问题记录**: 记录和跟踪编码问题

## 6. 团队协作规范

### 6.1 开发环境统一
- **IDE配置**: 团队成员使用相同的编码配置
- **工具版本**: 统一开发工具版本和设置
- **模板共享**: 共享标准化的文件模板
- **培训要求**: 新成员必须接受编码规范培训

### 6.2 版本控制规范
```gitignore
# .gitattributes 文件配置
*.c text eol=lf encoding=utf-8
*.h text eol=lf encoding=utf-8
*.md text eol=lf encoding=utf-8
```

### 6.3 持续集成检查
- **自动检查**: CI/CD流程中加入编码格式检查
- **失败阻断**: 编码格式错误时阻断构建
- **报告生成**: 生成编码质量报告
- **问题通知**: 自动通知相关开发人员

## 7. 常见问题与解决方案

### 7.1 FAQ
**Q: 为什么选择UTF-8而不是GBK？**
A: UTF-8是国际标准，兼容性最好，支持所有Unicode字符，是现代软件开发的首选编码。

**Q: 如何处理历史遗留的编码问题？**
A: 逐步转换，优先处理核心文件，使用自动化工具辅助转换。

**Q: 编码转换会影响代码功能吗？**
A: 不会，编码转换只影响字符显示，不影响代码逻辑和功能。

### 7.2 应急处理
- **紧急修复**: 发现编码问题时立即修复
- **回滚机制**: 保留原文件备份，支持快速回滚
- **影响评估**: 评估编码问题对项目的影响范围
- **预防措施**: 加强编码规范的执行和监督

## 8. 规范执行与监督

### 8.1 执行责任
- **项目经理**: 负责规范的制定和推广
- **技术负责人**: 负责规范的技术实施
- **开发人员**: 负责日常开发中的规范遵守
- **质量保证**: 负责规范执行的检查和监督

### 8.2 违规处理
- **轻微违规**: 提醒和指导
- **重复违规**: 强制培训
- **严重违规**: 代码审查不通过
- **持续违规**: 纳入绩效考核

## 9. 规范更新与维护

### 9.1 更新机制
- **定期评估**: 每季度评估规范的适用性
- **问题反馈**: 收集团队成员的反馈意见
- **版本管理**: 规范文档进行版本控制
- **变更通知**: 规范更新时及时通知全团队

### 9.2 培训计划
- **新员工培训**: 入职时进行编码规范培训
- **定期培训**: 每半年进行规范复习培训
- **工具培训**: 新工具使用的专项培训
- **最佳实践**: 分享编码规范的最佳实践

---

**本规范自发布之日起生效，所有团队成员必须严格遵守。**
