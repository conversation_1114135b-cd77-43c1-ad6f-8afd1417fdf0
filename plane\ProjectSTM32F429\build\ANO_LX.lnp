--cpu=Cortex-M4.fp.sp
".\build\main.o"
".\build\ano_scheduler.o"
".\build\user_task.o"
".\build\datatransfer.o"
".\build\lx_extsensor.o"
".\build\lx_fcfunc.o"
".\build\lx_fcstate.o"
".\build\lx_lowlevelfunc.o"
".\build\crc.o"
".\build\g_port.o"
".\build\mid360.o"
".\build\pid.o"
".\build\zigbee.o"
".\build\maixcam.o"
".\build\tofsense-m.o"
".\build\tofmini.o"
".\build\path_storage.o"
".\build\anoptv8exapi.o"
".\build\anoptv8run.o"
".\build\anoptv8cmd.o"
".\build\anoptv8par.o"
".\build\anoptv8framefactory.o"
".\build\drv_bsp.o"
".\build\ano_math.o"
".\build\drv_anoof.o"
".\build\drv_ubloxgps.o"
".\build\drvanoof_ptv7.o"
".\build\stm32f4xx_it.o"
".\build\drv_sys.o"
".\build\drv_led.o"
".\build\drv_timer.o"
".\build\drv_uart.o"
".\build\drv_pwmout.o"
".\build\drv_dshot600.o"
".\build\drv_rcin.o"
".\build\drv_usb.o"
".\build\drv_adc.o"
".\build\system_stm32f4xx.o"
".\build\misc.o"
".\build\stm32f4xx_adc.o"
".\build\stm32f4xx_dma.o"
".\build\stm32f4xx_exti.o"
".\build\stm32f4xx_flash.o"
".\build\stm32f4xx_gpio.o"
".\build\stm32f4xx_rcc.o"
".\build\stm32f4xx_spi.o"
".\build\stm32f4xx_tim.o"
".\build\stm32f4xx_usart.o"
".\build\startup_stm32f429_439xx.o"
".\build\usb_dc_dwc2.o"
".\build\usbd_cdc.o"
".\build\usbd_core.o"
--library_type=microlib --strict --scatter ".\build\ANO_LX.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\build\ANO_LX.map" -o .\build\ANO_LX.axf