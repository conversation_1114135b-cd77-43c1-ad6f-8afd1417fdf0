*** Using Compiler 'V5.06 update 7 (build 960)', folder: 'D:\keil5\ARM\ARMCC\Bin'
Build target 'Ano_LX'
compiling zigbee.c...
..\FcSrc\User\zigbee.c(62): warning:  #550-D: variable "animal_records"  was set but never used
  static animal_record_t animal_records[MAX_ANIMAL_RECORDS];  // 动物发现记录数组
..\FcSrc\User\zigbee.c(69): warning:  #550-D: variable "coordinate_offset_x"  was set but never used
  static s16 coordinate_offset_x = 0;        // X轴偏移量
..\FcSrc\User\zigbee.c(70): warning:  #550-D: variable "coordinate_offset_y"  was set but never used
  static s16 coordinate_offset_y = 0;        // Y轴偏移量
..\FcSrc\User\zigbee.c: 3 warnings, 0 errors
compiling Drv_Uart.c...
compiling PID.c...
compiling Maixcam.c...
compiling User_Task.c...
..\FcSrc\User_Task.c(1325): warning:  #177-D: variable "ch8_value"  was declared but never referenced
  	uint16_t ch8_value = rc_in.rc_ch.st_data.ch_[ch_8_aux4];
..\FcSrc\User_Task.c(1694): warning:  #177-D: function "calculate_descent_progress"  was declared but never referenced
  static float calculate_descent_progress(void)
..\FcSrc\User_Task.c(154): warning:  #177-D: variable "dadian_f"  was declared but never referenced
  static u8 dadian_f = 0;
..\FcSrc\User_Task.c(161): warning:  #177-D: variable "patrol_points_completed"  was declared but never referenced
  static int patrol_points_completed = 0;        // 已完成巡查点数量
..\FcSrc\User_Task.c(175): warning:  #550-D: variable "descent_angle"  was set but never used
  static float descent_angle = 45.0f;            // 降落角度
..\FcSrc\User_Task.c(210): warning:  #550-D: variable "current_patrol_step"  was set but never used
  static int current_patrol_step = 0;            // 当前巡查步骤索引
..\FcSrc\User_Task.c(340): warning:  #177-D: function "is_dadian_down_command"  was declared but never referenced
  static inline bool is_dadian_down_command(uint16_t ch_value) {
..\FcSrc\User_Task.c(364): warning:  #177-D: function "is_CAM_reached"  was declared but never referenced
  static inline bool is_CAM_reached(void) {
..\FcSrc\User_Task.c(571): warning:  #177-D: function "handle_patrol_navigation"  was declared but never referenced
  static void handle_patrol_navigation(int patrol_step)
..\FcSrc\User_Task.c: 9 warnings, 0 errors
linking...
Program Size: Code=94720 RO-data=12516 RW-data=3296 ZI-data=22000  
FromELF: creating hex file...
After Build - User command #1: fromelf.exe --bin -o ./ANO-LX.bin ./build/Ano_LX.axf
".\build\ANO_LX.axf" - 0 Error(s), 12 Warning(s).
Build Time Elapsed:  00:00:03
