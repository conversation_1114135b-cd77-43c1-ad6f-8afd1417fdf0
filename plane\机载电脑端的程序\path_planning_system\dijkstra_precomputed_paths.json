{"metadata": {"generation_time": "2025-07-31 18:08:14", "total_combinations": 92, "valid_paths": 92, "computation_time_seconds": 28.817367792129517, "parameters": {"patrol_time_per_point": 10.0, "travel_time_per_unit": 1.0}}, "path_data": [{"no_fly_zones": [11, 21, 31], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 42, 32, 22, 12, 13, 23, 33, 43, 53, 52, 62, 72, 82, 92, 93, 83, 73, 63, 64, 54, 44, 34, 24, 14, 15, 25, 35, 45, 55, 65, 75, 74, 84, 94, 95, 85, 86, 76, 66, 56, 46, 36, 26, 16, 17, 27, 37, 47, 57, 67, 77, 87, 97, 96], "patrol_path_length": 60, "patrol_time": 649.0, "patrol_distance": 59.0, "return_path_sequence": [96, 95, 94, 93, 92, 91], "return_path_length": 6, "return_time": 5.0, "return_distance": 5.0, "total_flight_time": 654.0, "computation_time_ms": 352.7412414550781, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [21, 31, 41], "patrol_path_sequence": [91, 81, 71, 61, 51, 52, 42, 32, 22, 12, 11, 13, 23, 33, 43, 53, 63, 62, 72, 82, 92, 93, 83, 73, 74, 64, 54, 44, 34, 24, 14, 15, 25, 35, 45, 55, 65, 75, 85, 84, 94, 95, 96, 86, 76, 66, 56, 46, 36, 26, 16, 17, 27, 37, 47, 57, 67, 77, 87, 97], "patrol_path_length": 60, "patrol_time": 650.0, "patrol_distance": 60.0, "return_path_sequence": [97, 96, 95, 94, 93, 92, 91], "return_path_length": 7, "return_time": 6.0, "return_distance": 6.0, "total_flight_time": 656.0, "computation_time_ms": 309.5567226409912, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [31, 41, 51], "patrol_path_sequence": [91, 81, 71, 61, 62, 52, 42, 32, 22, 21, 11, 12, 13, 23, 33, 43, 53, 63, 73, 72, 82, 92, 93, 83, 84, 74, 64, 54, 44, 34, 24, 14, 15, 25, 35, 45, 55, 65, 75, 85, 95, 94, 96, 86, 76, 66, 56, 46, 36, 26, 16, 17, 27, 37, 47, 57, 67, 77, 87, 97], "patrol_path_length": 60, "patrol_time": 650.0, "patrol_distance": 60.0, "return_path_sequence": [97, 96, 95, 94, 93, 92, 91], "return_path_length": 7, "return_time": 6.0, "return_distance": 6.0, "total_flight_time": 656.0, "computation_time_ms": 306.28490447998047, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [41, 51, 61], "patrol_path_sequence": [91, 81, 71, 72, 62, 52, 42, 32, 31, 21, 11, 12, 22, 23, 13, 14, 24, 34, 33, 43, 53, 63, 73, 83, 82, 92, 93, 94, 84, 74, 64, 54, 44, 45, 35, 25, 15, 16, 26, 36, 46, 56, 55, 65, 75, 85, 95, 96, 86, 76, 66, 67, 57, 47, 37, 27, 17, 77, 87, 97], "patrol_path_length": 60, "patrol_time": 654.0, "patrol_distance": 64.0, "return_path_sequence": [97, 96, 95, 94, 93, 92, 91], "return_path_length": 7, "return_time": 6.0, "return_distance": 6.0, "total_flight_time": 660.0, "computation_time_ms": 317.79932975769043, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [51, 61, 71], "patrol_path_sequence": [91, 81, 82, 72, 62, 52, 42, 41, 31, 21, 11, 12, 22, 32, 33, 23, 13, 14, 24, 34, 44, 43, 53, 63, 73, 83, 93, 92, 94, 84, 74, 64, 54, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 65, 75, 85, 95, 96, 86, 76, 77, 67, 57, 47, 37, 27, 17, 87, 97], "patrol_path_length": 60, "patrol_time": 656.0, "patrol_distance": 66.0, "return_path_sequence": [97, 96, 95, 94, 93, 92, 91], "return_path_length": 7, "return_time": 6.0, "return_distance": 6.0, "total_flight_time": 662.0, "computation_time_ms": 300.63891410827637, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [61, 71, 81], "patrol_path_sequence": [91, 92, 82, 72, 62, 52, 51, 41, 31, 21, 11, 12, 22, 32, 42, 43, 33, 23, 13, 14, 24, 34, 44, 54, 53, 63, 73, 83, 93, 94, 84, 74, 64, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 75, 85, 95, 96, 86, 87, 77, 67, 57, 47, 37, 27, 17, 97], "patrol_path_length": 60, "patrol_time": 656.0, "patrol_distance": 66.0, "return_path_sequence": [97, 96, 95, 94, 93, 92, 91], "return_path_length": 7, "return_time": 6.0, "return_distance": 6.0, "total_flight_time": 662.0, "computation_time_ms": 310.3599548339844, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [12, 22, 32], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 86, 96, 97, 87, 77, 67, 57, 47, 37, 27, 17], "patrol_path_length": 60, "patrol_time": 651.1622776601685, "patrol_distance": 61.16227766016838, "return_path_sequence": [17, 26, 35, 44, 53, 62, 72, 81, 91], "return_path_length": 9, "return_time": 10.485281374238571, "return_distance": 10.485281374238571, "total_flight_time": 661.647559034407, "computation_time_ms": 310.5804920196533, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [22, 32, 42], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 13, 23, 33, 43, 53, 52, 62, 72, 82, 92, 93, 83, 73, 63, 64, 54, 44, 34, 24, 14, 15, 25, 35, 45, 55, 65, 75, 74, 84, 94, 95, 85, 86, 76, 66, 56, 46, 36, 26, 16, 17, 27, 37, 47, 57, 67, 77, 87, 97, 96], "patrol_path_length": 60, "patrol_time": 649.0, "patrol_distance": 59.0, "return_path_sequence": [96, 95, 94, 93, 92, 91], "return_path_length": 6, "return_time": 5.0, "return_distance": 5.0, "total_flight_time": 654.0, "computation_time_ms": 304.97145652770996, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [32, 42, 52], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 23, 13, 14, 24, 34, 33, 43, 53, 63, 62, 72, 82, 92, 93, 83, 73, 74, 64, 54, 44, 45, 35, 25, 15, 16, 26, 36, 46, 56, 55, 65, 75, 85, 84, 94, 95, 96, 86, 76, 66, 67, 57, 47, 37, 27, 17, 77, 87, 97], "patrol_path_length": 60, "patrol_time": 654.0, "patrol_distance": 64.0, "return_path_sequence": [97, 96, 95, 94, 93, 92, 91], "return_path_length": 7, "return_time": 6.0, "return_distance": 6.0, "total_flight_time": 660.0, "computation_time_ms": 291.89229011535645, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [42, 52, 62], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 33, 23, 13, 14, 24, 34, 44, 43, 53, 63, 73, 72, 82, 92, 93, 83, 84, 74, 64, 54, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 65, 75, 85, 95, 94, 96, 86, 76, 77, 67, 57, 47, 37, 27, 17, 87, 97], "patrol_path_length": 60, "patrol_time": 656.0, "patrol_distance": 66.0, "return_path_sequence": [97, 96, 95, 94, 93, 92, 91], "return_path_length": 7, "return_time": 6.0, "return_distance": 6.0, "total_flight_time": 662.0, "computation_time_ms": 292.8197383880615, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [52, 62, 72], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 43, 33, 23, 13, 14, 24, 34, 44, 54, 53, 63, 73, 83, 82, 92, 93, 94, 84, 74, 64, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 75, 85, 95, 96, 86, 87, 77, 67, 57, 47, 37, 27, 17, 97], "patrol_path_length": 60, "patrol_time": 656.0, "patrol_distance": 66.0, "return_path_sequence": [97, 96, 95, 94, 93, 92, 91], "return_path_length": 7, "return_time": 6.0, "return_distance": 6.0, "total_flight_time": 662.0, "computation_time_ms": 286.58437728881836, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [62, 72, 82], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 63, 73, 83, 93, 92, 94, 84, 74, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 86, 85, 95, 96, 97, 87, 77, 67, 57, 47, 37, 27, 17], "patrol_path_length": 60, "patrol_time": 650.0, "patrol_distance": 60.0, "return_path_sequence": [17, 26, 25, 34, 43, 52, 51, 61, 71, 81, 91], "return_path_length": 11, "return_time": 11.65685424949238, "return_distance": 11.65685424949238, "total_flight_time": 661.6568542494924, "computation_time_ms": 287.29248046875, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [72, 82, 92], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 73, 83, 93, 94, 84, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 86, 96, 95, 97, 87, 77, 67, 57, 47, 37, 27, 17], "patrol_path_length": 60, "patrol_time": 650.0, "patrol_distance": 60.0, "return_path_sequence": [17, 26, 35, 44, 53, 62, 61, 71, 81, 91], "return_path_length": 10, "return_time": 11.071067811865476, "return_distance": 11.071067811865476, "total_flight_time": 661.0710678118655, "computation_time_ms": 301.1600971221924, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [13, 23, 33], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 44, 34, 24, 14, 15, 25, 35, 45, 55, 54, 64, 74, 84, 94, 95, 85, 75, 65, 66, 56, 46, 36, 26, 16, 17, 27, 37, 47, 57, 67, 77, 76, 86, 96, 97, 87], "patrol_path_length": 60, "patrol_time": 649.0, "patrol_distance": 59.0, "return_path_sequence": [87, 86, 85, 84, 83, 82, 91], "return_path_length": 7, "return_time": 6.414213562373095, "return_distance": 6.414213562373095, "total_flight_time": 655.4142135623731, "computation_time_ms": 296.7102527618408, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [23, 33, 43], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 54, 44, 34, 24, 14, 13, 15, 25, 35, 45, 55, 65, 64, 74, 84, 94, 95, 85, 75, 76, 66, 56, 46, 36, 26, 16, 17, 27, 37, 47, 57, 67, 77, 87, 86, 96, 97], "patrol_path_length": 60, "patrol_time": 650.0, "patrol_distance": 60.0, "return_path_sequence": [97, 96, 95, 94, 93, 92, 91], "return_path_length": 7, "return_time": 6.0, "return_distance": 6.0, "total_flight_time": 656.0, "computation_time_ms": 288.4254455566406, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [33, 43, 53], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 64, 54, 44, 34, 24, 23, 13, 14, 15, 25, 35, 45, 55, 65, 75, 74, 84, 94, 95, 85, 86, 76, 66, 56, 46, 36, 26, 16, 17, 27, 37, 47, 57, 67, 77, 87, 97, 96], "patrol_path_length": 60, "patrol_time": 649.0, "patrol_distance": 59.0, "return_path_sequence": [96, 95, 94, 93, 92, 91], "return_path_length": 6, "return_time": 5.0, "return_distance": 5.0, "total_flight_time": 654.0, "computation_time_ms": 287.59312629699707, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [43, 53, 63], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 74, 64, 54, 44, 34, 33, 23, 13, 14, 24, 25, 15, 16, 26, 36, 35, 45, 55, 65, 75, 85, 84, 94, 95, 96, 86, 76, 66, 56, 46, 47, 37, 27, 17, 57, 67, 77, 87, 97], "patrol_path_length": 60, "patrol_time": 652.0, "patrol_distance": 62.0, "return_path_sequence": [97, 96, 95, 94, 93, 92, 91], "return_path_length": 7, "return_time": 6.0, "return_distance": 6.0, "total_flight_time": 658.0, "computation_time_ms": 302.39248275756836, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [53, 63, 73], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 84, 74, 64, 54, 44, 43, 33, 23, 13, 14, 24, 34, 35, 25, 15, 16, 26, 36, 46, 45, 55, 65, 75, 85, 95, 94, 96, 86, 76, 66, 56, 57, 47, 37, 27, 17, 67, 77, 87, 97], "patrol_path_length": 60, "patrol_time": 654.0, "patrol_distance": 64.0, "return_path_sequence": [97, 96, 95, 94, 93, 92, 91], "return_path_length": 7, "return_time": 6.0, "return_distance": 6.0, "total_flight_time": 660.0, "computation_time_ms": 326.62439346313477, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [63, 73, 83], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 94, 84, 74, 64, 54, 53, 43, 33, 23, 13, 14, 24, 34, 44, 45, 35, 25, 15, 16, 26, 36, 46, 56, 55, 65, 75, 85, 95, 96, 86, 76, 66, 67, 57, 47, 37, 27, 17, 77, 87, 97], "patrol_path_length": 60, "patrol_time": 654.0, "patrol_distance": 64.0, "return_path_sequence": [97, 96, 95, 94, 93, 92, 91], "return_path_length": 7, "return_time": 6.0, "return_distance": 6.0, "total_flight_time": 660.0, "computation_time_ms": 313.9207363128662, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [73, 83, 93], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 86, 96, 97, 87, 77, 67, 57, 47, 37, 27, 17], "patrol_path_length": 60, "patrol_time": 651.1622776601683, "patrol_distance": 61.16227766016838, "return_path_sequence": [17, 26, 35, 44, 53, 62, 72, 81, 91], "return_path_length": 9, "return_time": 10.485281374238571, "return_distance": 10.485281374238571, "total_flight_time": 661.6475590344069, "computation_time_ms": 318.78113746643066, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [14, 24, 34], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 86, 96, 97, 87, 77, 67, 57, 47, 37, 27, 17], "patrol_path_length": 60, "patrol_time": 651.1622776601685, "patrol_distance": 61.16227766016838, "return_path_sequence": [17, 26, 36, 45, 54, 63, 72, 81, 91], "return_path_length": 9, "return_time": 10.485281374238571, "return_distance": 10.485281374238571, "total_flight_time": 661.647559034407, "computation_time_ms": 356.7314147949219, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [24, 34, 44], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 15, 25, 35, 45, 55, 54, 64, 74, 84, 94, 95, 85, 75, 65, 66, 56, 46, 36, 26, 16, 17, 27, 37, 47, 57, 67, 77, 76, 86, 96, 97, 87], "patrol_path_length": 60, "patrol_time": 649.0, "patrol_distance": 59.0, "return_path_sequence": [87, 86, 85, 84, 83, 82, 91], "return_path_length": 7, "return_time": 6.414213562373095, "return_distance": 6.414213562373095, "total_flight_time": 655.4142135623731, "computation_time_ms": 295.7446575164795, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [34, 44, 54], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 25, 15, 16, 26, 36, 35, 45, 55, 65, 64, 74, 84, 94, 95, 85, 75, 76, 66, 56, 46, 47, 37, 27, 17, 57, 67, 77, 87, 86, 96, 97], "patrol_path_length": 60, "patrol_time": 652.0, "patrol_distance": 62.0, "return_path_sequence": [97, 96, 95, 94, 93, 92, 91], "return_path_length": 7, "return_time": 6.0, "return_distance": 6.0, "total_flight_time": 658.0, "computation_time_ms": 297.92308807373047, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [44, 54, 64], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 35, 25, 15, 16, 26, 36, 46, 45, 55, 65, 75, 74, 84, 94, 95, 85, 86, 76, 66, 56, 57, 47, 37, 27, 17, 67, 77, 87, 97, 96], "patrol_path_length": 60, "patrol_time": 653.0, "patrol_distance": 63.0, "return_path_sequence": [96, 95, 94, 93, 92, 91], "return_path_length": 6, "return_time": 5.0, "return_distance": 5.0, "total_flight_time": 658.0, "computation_time_ms": 287.13393211364746, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [54, 64, 74], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 45, 35, 25, 15, 16, 26, 36, 46, 56, 55, 65, 75, 85, 84, 94, 95, 96, 86, 76, 66, 67, 57, 47, 37, 27, 17, 77, 87, 97], "patrol_path_length": 60, "patrol_time": 654.0, "patrol_distance": 64.0, "return_path_sequence": [97, 96, 95, 94, 93, 92, 91], "return_path_length": 7, "return_time": 6.0, "return_distance": 6.0, "total_flight_time": 660.0, "computation_time_ms": 295.2761650085449, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [64, 74, 84], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 65, 75, 85, 95, 94, 96, 86, 76, 77, 67, 57, 47, 37, 27, 17, 87, 97], "patrol_path_length": 60, "patrol_time": 656.0, "patrol_distance": 66.0, "return_path_sequence": [97, 96, 95, 94, 93, 92, 91], "return_path_length": 7, "return_time": 6.0, "return_distance": 6.0, "total_flight_time": 662.0, "computation_time_ms": 295.7727909088135, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [74, 84, 94], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 75, 85, 95, 96, 86, 87, 77, 67, 57, 47, 37, 27, 17, 97], "patrol_path_length": 60, "patrol_time": 656.0, "patrol_distance": 66.0, "return_path_sequence": [97, 86, 76, 65, 64, 63, 72, 81, 91], "return_path_length": 9, "return_time": 9.65685424949238, "return_distance": 9.65685424949238, "total_flight_time": 665.6568542494924, "computation_time_ms": 298.83885383605957, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [15, 25, 35], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 46, 36, 26, 16, 17, 27, 37, 47, 57, 56, 66, 76, 86, 96, 97, 87, 77, 67], "patrol_path_length": 60, "patrol_time": 649.0, "patrol_distance": 59.0, "return_path_sequence": [67, 76, 75, 84, 83, 82, 91], "return_path_length": 7, "return_time": 7.242640687119285, "return_distance": 7.242640687119285, "total_flight_time": 656.2426406871193, "computation_time_ms": 323.3296871185303, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [25, 35, 45], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 56, 46, 36, 26, 16, 15, 17, 27, 37, 47, 57, 67, 66, 76, 86, 96, 97, 87, 77], "patrol_path_length": 60, "patrol_time": 650.0, "patrol_distance": 60.0, "return_path_sequence": [77, 76, 75, 84, 83, 82, 91], "return_path_length": 7, "return_time": 6.82842712474619, "return_distance": 6.82842712474619, "total_flight_time": 656.8284271247462, "computation_time_ms": 299.57032203674316, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [35, 45, 55], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 66, 56, 46, 36, 26, 25, 15, 16, 17, 27, 37, 47, 57, 67, 77, 76, 86, 96, 97, 87], "patrol_path_length": 60, "patrol_time": 649.0, "patrol_distance": 59.0, "return_path_sequence": [87, 86, 85, 84, 83, 82, 91], "return_path_length": 7, "return_time": 6.414213562373095, "return_distance": 6.414213562373095, "total_flight_time": 655.4142135623731, "computation_time_ms": 318.52197647094727, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [45, 55, 65], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 76, 66, 56, 46, 36, 35, 25, 15, 16, 26, 27, 17, 37, 47, 57, 67, 77, 87, 86, 96, 97], "patrol_path_length": 60, "patrol_time": 650.0, "patrol_distance": 60.0, "return_path_sequence": [97, 96, 95, 94, 93, 92, 91], "return_path_length": 7, "return_time": 6.0, "return_distance": 6.0, "total_flight_time": 656.0, "computation_time_ms": 300.31824111938477, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [55, 65, 75], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 86, 76, 66, 56, 46, 45, 35, 25, 15, 16, 26, 36, 37, 27, 17, 47, 57, 67, 77, 87, 97, 96], "patrol_path_length": 60, "patrol_time": 651.0, "patrol_distance": 61.0, "return_path_sequence": [96, 95, 94, 93, 92, 91], "return_path_length": 6, "return_time": 5.0, "return_distance": 5.0, "total_flight_time": 656.0, "computation_time_ms": 305.0341606140137, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [65, 75, 85], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 96, 86, 76, 66, 56, 55, 45, 35, 25, 15, 16, 26, 36, 46, 47, 37, 27, 17, 57, 67, 77, 87, 97], "patrol_path_length": 60, "patrol_time": 652.0, "patrol_distance": 62.0, "return_path_sequence": [97, 96, 95, 94, 93, 92, 91], "return_path_length": 7, "return_time": 6.0, "return_distance": 6.0, "total_flight_time": 658.0, "computation_time_ms": 298.7511157989502, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [75, 85, 95], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 86, 96, 97, 87, 77, 67, 57, 47, 37, 27, 17], "patrol_path_length": 60, "patrol_time": 651.1622776601685, "patrol_distance": 61.16227766016838, "return_path_sequence": [17, 26, 35, 44, 53, 62, 72, 81, 91], "return_path_length": 9, "return_time": 10.485281374238571, "return_distance": 10.485281374238571, "total_flight_time": 661.647559034407, "computation_time_ms": 308.7153434753418, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [16, 26, 36], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 15, 46, 56, 66, 76, 86, 96, 97, 87, 77, 67, 57, 47, 37, 27, 17], "patrol_path_length": 60, "patrol_time": 651.1622776601685, "patrol_distance": 61.16227766016838, "return_path_sequence": [17, 27, 37, 47, 56, 65, 74, 83, 82, 91], "return_path_length": 10, "return_time": 11.071067811865476, "return_distance": 11.071067811865476, "total_flight_time": 662.233345472034, "computation_time_ms": 302.0052909851074, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [26, 36, 46], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 15, 16, 17, 27, 37, 47, 57, 56, 66, 76, 86, 96, 97, 87, 77, 67], "patrol_path_length": 60, "patrol_time": 649.0, "patrol_distance": 59.0, "return_path_sequence": [67, 76, 75, 84, 83, 82, 91], "return_path_length": 7, "return_time": 7.242640687119285, "return_distance": 7.242640687119285, "total_flight_time": 656.2426406871193, "computation_time_ms": 315.42348861694336, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [36, 46, 56], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 27, 17, 37, 47, 57, 67, 66, 76, 86, 96, 97, 87, 77], "patrol_path_length": 60, "patrol_time": 650.0, "patrol_distance": 60.0, "return_path_sequence": [77, 76, 75, 84, 83, 82, 91], "return_path_length": 7, "return_time": 6.82842712474619, "return_distance": 6.82842712474619, "total_flight_time": 656.8284271247462, "computation_time_ms": 304.0592670440674, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [46, 56, 66], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 37, 27, 17, 47, 57, 67, 77, 76, 86, 96, 97, 87], "patrol_path_length": 60, "patrol_time": 651.0, "patrol_distance": 61.0, "return_path_sequence": [87, 86, 85, 84, 83, 82, 91], "return_path_length": 7, "return_time": 6.414213562373095, "return_distance": 6.414213562373095, "total_flight_time": 657.4142135623731, "computation_time_ms": 317.57616996765137, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [56, 66, 76], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 47, 37, 27, 17, 57, 67, 77, 87, 86, 96, 97], "patrol_path_length": 60, "patrol_time": 652.0, "patrol_distance": 62.0, "return_path_sequence": [97, 96, 95, 94, 93, 92, 91], "return_path_length": 7, "return_time": 6.0, "return_distance": 6.0, "total_flight_time": 658.0, "computation_time_ms": 344.3760871887207, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [66, 76, 86], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 57, 47, 37, 27, 17, 67, 77, 87, 97, 96], "patrol_path_length": 60, "patrol_time": 653.0, "patrol_distance": 63.0, "return_path_sequence": [96, 95, 94, 93, 92, 91], "return_path_length": 6, "return_time": 5.0, "return_distance": 5.0, "total_flight_time": 658.0, "computation_time_ms": 329.3416500091553, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [76, 86, 96], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 67, 57, 47, 37, 27, 17, 77, 87, 97], "patrol_path_length": 60, "patrol_time": 654.0, "patrol_distance": 64.0, "return_path_sequence": [97, 87, 77, 67, 66, 65, 74, 83, 82, 91], "return_path_length": 10, "return_time": 10.242640687119286, "return_distance": 10.242640687119286, "total_flight_time": 664.2426406871193, "computation_time_ms": 315.82021713256836, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [17, 27, 37], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 86, 96, 97, 87, 77, 67, 57, 47], "patrol_path_length": 60, "patrol_time": 649.0, "patrol_distance": 59.0, "return_path_sequence": [47, 56, 65, 74, 83, 82, 91], "return_path_length": 7, "return_time": 8.071067811865476, "return_distance": 8.071067811865476, "total_flight_time": 657.0710678118655, "computation_time_ms": 343.69921684265137, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [27, 37, 47], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 86, 96, 97, 87, 77, 67, 57, 17], "patrol_path_length": 60, "patrol_time": 652.0, "patrol_distance": 62.0, "return_path_sequence": [17, 16, 25, 34, 43, 53, 62, 72, 81, 91], "return_path_length": 10, "return_time": 11.071067811865476, "return_distance": 11.071067811865476, "total_flight_time": 663.0710678118655, "computation_time_ms": 332.4418067932129, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [37, 47, 57], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 86, 96, 97, 87, 77, 67, 27, 17], "patrol_path_length": 60, "patrol_time": 652.0, "patrol_distance": 62.0, "return_path_sequence": [17, 26, 35, 44, 53, 62, 72, 81, 91], "return_path_length": 9, "return_time": 10.485281374238571, "return_distance": 10.485281374238571, "total_flight_time": 662.4852813742385, "computation_time_ms": 326.9357681274414, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [47, 57, 67], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 86, 96, 97, 87, 77, 37, 27, 17], "patrol_path_length": 60, "patrol_time": 652.0, "patrol_distance": 62.0, "return_path_sequence": [17, 26, 35, 44, 53, 62, 72, 81, 91], "return_path_length": 9, "return_time": 10.485281374238571, "return_distance": 10.485281374238571, "total_flight_time": 662.4852813742385, "computation_time_ms": 312.5936985015869, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [57, 67, 77], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 86, 96, 97, 87, 47, 37, 27, 17], "patrol_path_length": 60, "patrol_time": 652.0, "patrol_distance": 62.0, "return_path_sequence": [17, 26, 35, 44, 53, 62, 72, 81, 91], "return_path_length": 9, "return_time": 10.485281374238571, "return_distance": 10.485281374238571, "total_flight_time": 662.4852813742385, "computation_time_ms": 313.7972354888916, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [67, 77, 87], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 86, 96, 97, 57, 47, 37, 27, 17], "patrol_path_length": 60, "patrol_time": 652.0, "patrol_distance": 62.0, "return_path_sequence": [17, 26, 35, 44, 53, 62, 72, 81, 91], "return_path_length": 9, "return_time": 10.485281374238571, "return_distance": 10.485281374238571, "total_flight_time": 662.4852813742385, "computation_time_ms": 311.44165992736816, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [77, 87, 97], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 86, 96, 67, 57, 47, 37, 27, 17], "patrol_path_length": 60, "patrol_time": 651.1622776601683, "patrol_distance": 61.16227766016838, "return_path_sequence": [17, 26, 35, 44, 53, 62, 72, 81, 91], "return_path_length": 9, "return_time": 10.485281374238571, "return_distance": 10.485281374238571, "total_flight_time": 661.6475590344069, "computation_time_ms": 302.2022247314453, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [11, 12, 13], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 24, 14, 15, 25, 35, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 46, 36, 26, 16, 17, 27, 37, 47, 57, 56, 66, 76, 86, 96, 97, 87, 77, 67], "patrol_path_length": 60, "patrol_time": 649.0, "patrol_distance": 59.0, "return_path_sequence": [67, 76, 75, 84, 83, 82, 91], "return_path_length": 7, "return_time": 7.242640687119285, "return_distance": 7.242640687119285, "total_flight_time": 656.2426406871193, "computation_time_ms": 308.06541442871094, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [12, 13, 14], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 86, 96, 97, 87, 77, 67, 57, 47, 37, 27, 17], "patrol_path_length": 60, "patrol_time": 649.4142135623731, "patrol_distance": 59.41421356237309, "return_path_sequence": [17, 26, 35, 44, 53, 62, 72, 81, 91], "return_path_length": 9, "return_time": 10.485281374238571, "return_distance": 10.485281374238571, "total_flight_time": 659.8994949366116, "computation_time_ms": 311.5730285644531, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [13, 14, 15], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 26, 16, 17, 27, 37, 36, 46, 56, 66, 76, 86, 96, 97, 87, 77, 67, 57, 47], "patrol_path_length": 60, "patrol_time": 649.0, "patrol_distance": 59.0, "return_path_sequence": [47, 56, 65, 74, 83, 82, 91], "return_path_length": 7, "return_time": 8.071067811865476, "return_distance": 8.071067811865476, "total_flight_time": 657.0710678118655, "computation_time_ms": 328.08947563171387, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [14, 15, 16], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 26, 36, 46, 56, 66, 76, 86, 96, 97, 87, 77, 67, 57, 47, 37, 27, 17], "patrol_path_length": 60, "patrol_time": 649.4142135623731, "patrol_distance": 59.41421356237309, "return_path_sequence": [17, 27, 36, 45, 54, 63, 72, 81, 91], "return_path_length": 9, "return_time": 10.485281374238571, "return_distance": 10.485281374238571, "total_flight_time": 659.8994949366116, "computation_time_ms": 323.199987411499, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [15, 16, 17], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 26, 36, 46, 56, 66, 76, 86, 96, 97, 87, 77, 67, 57, 47, 37, 27], "patrol_path_length": 60, "patrol_time": 649.0, "patrol_distance": 59.0, "return_path_sequence": [27, 36, 45, 54, 63, 72, 81, 91], "return_path_length": 8, "return_time": 9.485281374238571, "return_distance": 9.485281374238571, "total_flight_time": 658.4852813742385, "computation_time_ms": 308.3803653717041, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [21, 22, 23], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 34, 24, 14, 13, 12, 11, 15, 25, 35, 45, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 56, 46, 36, 26, 16, 17, 27, 37, 47, 57, 67, 66, 76, 86, 96, 97, 87, 77], "patrol_path_length": 60, "patrol_time": 652.0, "patrol_distance": 62.0, "return_path_sequence": [77, 76, 75, 84, 83, 82, 91], "return_path_length": 7, "return_time": 6.82842712474619, "return_distance": 6.82842712474619, "total_flight_time": 658.8284271247462, "computation_time_ms": 311.4197254180908, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [22, 23, 24], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 13, 14, 15, 25, 35, 34, 33, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 46, 36, 26, 16, 17, 27, 37, 47, 57, 56, 66, 76, 86, 96, 97, 87, 77, 67], "patrol_path_length": 60, "patrol_time": 649.0, "patrol_distance": 59.0, "return_path_sequence": [67, 76, 75, 84, 83, 82, 91], "return_path_length": 7, "return_time": 7.242640687119285, "return_distance": 7.242640687119285, "total_flight_time": 656.2426406871193, "computation_time_ms": 287.16588020324707, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [23, 24, 25], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 36, 26, 16, 15, 14, 13, 17, 27, 37, 47, 46, 56, 66, 76, 86, 96, 97, 87, 77, 67, 57], "patrol_path_length": 60, "patrol_time": 652.0, "patrol_distance": 62.0, "return_path_sequence": [57, 66, 65, 74, 83, 82, 91], "return_path_length": 7, "return_time": 7.65685424949238, "return_distance": 7.65685424949238, "total_flight_time": 659.6568542494924, "computation_time_ms": 298.66576194763184, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [24, 25, 26], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 15, 16, 17, 27, 37, 36, 35, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 46, 56, 66, 76, 86, 96, 97, 87, 77, 67, 57, 47], "patrol_path_length": 60, "patrol_time": 649.0, "patrol_distance": 59.0, "return_path_sequence": [47, 56, 65, 74, 83, 82, 91], "return_path_length": 7, "return_time": 8.071067811865476, "return_distance": 8.071067811865476, "total_flight_time": 657.0710678118655, "computation_time_ms": 326.1861801147461, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [25, 26, 27], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 36, 46, 56, 66, 76, 86, 96, 97, 87, 77, 67, 57, 47, 37, 15, 16, 17], "patrol_path_length": 60, "patrol_time": 650.8284271247462, "patrol_distance": 60.82842712474619, "return_path_sequence": [17, 16, 15, 14, 23, 32, 41, 51, 61, 71, 81, 91], "return_path_length": 12, "return_time": 12.242640687119284, "return_distance": 12.242640687119284, "total_flight_time": 663.0710678118655, "computation_time_ms": 410.98880767822266, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [31, 32, 33], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 44, 34, 24, 23, 22, 21, 11, 12, 13, 14, 15, 25, 35, 45, 55, 54, 64, 74, 84, 94, 95, 85, 75, 65, 66, 56, 46, 36, 26, 16, 17, 27, 37, 47, 57, 67, 77, 76, 86, 96, 97, 87], "patrol_path_length": 60, "patrol_time": 649.0, "patrol_distance": 59.0, "return_path_sequence": [87, 86, 85, 84, 83, 82, 91], "return_path_length": 7, "return_time": 6.414213562373095, "return_distance": 6.414213562373095, "total_flight_time": 655.4142135623731, "computation_time_ms": 356.83202743530273, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [32, 33, 34], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 23, 13, 14, 24, 25, 15, 16, 26, 36, 35, 45, 44, 43, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 56, 46, 47, 37, 27, 17, 57, 67, 66, 76, 86, 96, 97, 87, 77], "patrol_path_length": 60, "patrol_time": 652.0, "patrol_distance": 62.0, "return_path_sequence": [77, 76, 75, 84, 83, 82, 91], "return_path_length": 7, "return_time": 6.82842712474619, "return_distance": 6.82842712474619, "total_flight_time": 658.8284271247462, "computation_time_ms": 324.74279403686523, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [33, 34, 35], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 46, 36, 26, 25, 24, 23, 13, 14, 15, 16, 17, 27, 37, 47, 57, 56, 66, 76, 86, 96, 97, 87, 77, 67], "patrol_path_length": 60, "patrol_time": 649.0, "patrol_distance": 59.0, "return_path_sequence": [67, 76, 75, 84, 83, 82, 91], "return_path_length": 7, "return_time": 7.242640687119285, "return_distance": 7.242640687119285, "total_flight_time": 656.2426406871193, "computation_time_ms": 316.3332939147949, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [34, 35, 36], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 25, 15, 16, 26, 27, 17, 37, 47, 46, 45, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 56, 66, 76, 86, 96, 97, 87, 77, 67, 57], "patrol_path_length": 60, "patrol_time": 650.0, "patrol_distance": 60.0, "return_path_sequence": [57, 66, 65, 74, 83, 82, 91], "return_path_length": 7, "return_time": 7.65685424949238, "return_distance": 7.65685424949238, "total_flight_time": 657.6568542494924, "computation_time_ms": 300.87780952453613, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [35, 36, 37], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 46, 56, 66, 76, 86, 96, 97, 87, 77, 67, 57, 47, 25, 15, 16, 26, 27, 17], "patrol_path_length": 60, "patrol_time": 650.8284271247462, "patrol_distance": 60.82842712474619, "return_path_sequence": [17, 26, 25, 24, 34, 43, 52, 62, 72, 81, 91], "return_path_length": 11, "return_time": 11.65685424949238, "return_distance": 11.65685424949238, "total_flight_time": 662.4852813742386, "computation_time_ms": 348.8013744354248, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [41, 42, 43], "patrol_path_sequence": [91, 81, 71, 61, 51, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 54, 44, 34, 33, 32, 31, 21, 11, 12, 22, 23, 13, 14, 24, 25, 15, 16, 26, 36, 35, 45, 55, 65, 64, 74, 84, 94, 95, 85, 75, 76, 66, 56, 46, 47, 37, 27, 17, 57, 67, 77, 87, 86, 96, 97], "patrol_path_length": 60, "patrol_time": 652.0, "patrol_distance": 62.0, "return_path_sequence": [97, 96, 95, 94, 93, 92, 91], "return_path_length": 7, "return_time": 6.0, "return_distance": 6.0, "total_flight_time": 658.0, "computation_time_ms": 313.36522102355957, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [42, 43, 44], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 33, 23, 13, 14, 24, 34, 35, 25, 15, 16, 26, 36, 46, 45, 55, 54, 53, 52, 62, 72, 82, 92, 93, 83, 73, 63, 64, 74, 84, 94, 95, 85, 75, 65, 66, 56, 57, 47, 37, 27, 17, 67, 77, 76, 86, 96, 97, 87], "patrol_path_length": 60, "patrol_time": 653.0, "patrol_distance": 63.0, "return_path_sequence": [87, 86, 85, 84, 83, 82, 91], "return_path_length": 7, "return_time": 6.414213562373095, "return_distance": 6.414213562373095, "total_flight_time": 659.4142135623731, "computation_time_ms": 290.8306121826172, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [43, 44, 45], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 56, 46, 36, 35, 34, 33, 23, 13, 14, 24, 25, 15, 16, 26, 27, 17, 37, 47, 57, 67, 66, 76, 86, 96, 97, 87, 77], "patrol_path_length": 60, "patrol_time": 650.0, "patrol_distance": 60.0, "return_path_sequence": [77, 76, 75, 84, 83, 82, 91], "return_path_length": 7, "return_time": 6.82842712474619, "return_distance": 6.82842712474619, "total_flight_time": 656.8284271247462, "computation_time_ms": 291.34607315063477, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [44, 45, 46], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 35, 25, 15, 16, 26, 36, 37, 27, 17, 47, 57, 56, 55, 54, 64, 74, 84, 94, 95, 85, 75, 65, 66, 76, 86, 96, 97, 87, 77, 67], "patrol_path_length": 60, "patrol_time": 651.0, "patrol_distance": 61.0, "return_path_sequence": [67, 76, 75, 84, 83, 82, 91], "return_path_length": 7, "return_time": 7.242640687119285, "return_distance": 7.242640687119285, "total_flight_time": 658.2426406871193, "computation_time_ms": 296.2779998779297, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [45, 46, 47], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 56, 66, 76, 86, 96, 97, 87, 77, 67, 57, 35, 25, 15, 16, 26, 36, 37, 27, 17], "patrol_path_length": 60, "patrol_time": 650.8284271247462, "patrol_distance": 60.82842712474619, "return_path_sequence": [17, 26, 25, 34, 43, 53, 62, 72, 81, 91], "return_path_length": 10, "return_time": 11.071067811865476, "return_distance": 11.071067811865476, "total_flight_time": 661.8994949366117, "computation_time_ms": 317.10100173950195, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [51, 52, 53], "patrol_path_sequence": [91, 81, 71, 61, 62, 72, 82, 92, 93, 83, 73, 63, 64, 54, 44, 43, 42, 41, 31, 21, 11, 12, 22, 32, 33, 23, 13, 14, 24, 34, 35, 25, 15, 16, 26, 36, 46, 45, 55, 65, 75, 74, 84, 94, 95, 85, 86, 76, 66, 56, 57, 47, 37, 27, 17, 67, 77, 87, 97, 96], "patrol_path_length": 60, "patrol_time": 653.0, "patrol_distance": 63.0, "return_path_sequence": [96, 95, 94, 93, 92, 91], "return_path_length": 6, "return_time": 5.0, "return_distance": 5.0, "total_flight_time": 658.0, "computation_time_ms": 292.51646995544434, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [52, 53, 54], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 43, 33, 23, 13, 14, 24, 34, 44, 45, 35, 25, 15, 16, 26, 36, 46, 56, 55, 65, 64, 63, 62, 72, 82, 92, 93, 83, 73, 74, 84, 94, 95, 85, 75, 76, 66, 67, 57, 47, 37, 27, 17, 77, 87, 86, 96, 97], "patrol_path_length": 60, "patrol_time": 654.0, "patrol_distance": 64.0, "return_path_sequence": [97, 96, 95, 94, 93, 92, 91], "return_path_length": 7, "return_time": 6.0, "return_distance": 6.0, "total_flight_time": 660.0, "computation_time_ms": 281.7058563232422, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [53, 54, 55], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 64, 74, 84, 94, 95, 85, 75, 65, 66, 56, 46, 45, 44, 43, 33, 23, 13, 14, 24, 34, 35, 25, 15, 16, 26, 36, 37, 27, 17, 47, 57, 67, 77, 76, 86, 96, 97, 87], "patrol_path_length": 60, "patrol_time": 651.0, "patrol_distance": 61.0, "return_path_sequence": [87, 86, 85, 84, 83, 82, 91], "return_path_length": 7, "return_time": 6.414213562373095, "return_distance": 6.414213562373095, "total_flight_time": 657.4142135623731, "computation_time_ms": 301.0718822479248, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [54, 55, 56], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 45, 35, 25, 15, 16, 26, 36, 46, 47, 37, 27, 17, 57, 67, 66, 65, 64, 74, 84, 94, 95, 85, 75, 76, 86, 96, 97, 87, 77], "patrol_path_length": 60, "patrol_time": 652.0, "patrol_distance": 62.0, "return_path_sequence": [77, 76, 75, 84, 83, 82, 91], "return_path_length": 7, "return_time": 6.82842712474619, "return_distance": 6.82842712474619, "total_flight_time": 658.8284271247462, "computation_time_ms": 322.77536392211914, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [55, 56, 57], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 66, 76, 86, 96, 97, 87, 77, 67, 45, 35, 25, 15, 16, 26, 36, 46, 47, 37, 27, 17], "patrol_path_length": 60, "patrol_time": 650.8284271247462, "patrol_distance": 60.82842712474619, "return_path_sequence": [17, 26, 35, 44, 53, 62, 72, 81, 91], "return_path_length": 9, "return_time": 10.485281374238571, "return_distance": 10.485281374238571, "total_flight_time": 661.3137084989847, "computation_time_ms": 377.6674270629883, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [61, 62, 63], "patrol_path_sequence": [91, 81, 71, 72, 82, 92, 93, 83, 73, 74, 64, 54, 53, 52, 51, 41, 31, 21, 11, 12, 22, 32, 42, 43, 33, 23, 13, 14, 24, 34, 44, 45, 35, 25, 15, 16, 26, 36, 46, 56, 55, 65, 75, 85, 84, 94, 95, 96, 86, 76, 66, 67, 57, 47, 37, 27, 17, 77, 87, 97], "patrol_path_length": 60, "patrol_time": 654.0, "patrol_distance": 64.0, "return_path_sequence": [97, 96, 95, 94, 93, 92, 91], "return_path_length": 7, "return_time": 6.0, "return_distance": 6.0, "total_flight_time": 660.0, "computation_time_ms": 287.42265701293945, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [62, 63, 64], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 65, 75, 74, 73, 72, 82, 92, 93, 83, 84, 94, 95, 85, 86, 76, 77, 67, 57, 47, 37, 27, 17, 87, 97, 96], "patrol_path_length": 60, "patrol_time": 655.0, "patrol_distance": 65.0, "return_path_sequence": [96, 95, 94, 93, 92, 91], "return_path_length": 6, "return_time": 5.0, "return_distance": 5.0, "total_flight_time": 660.0, "computation_time_ms": 290.94600677490234, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [63, 64, 65], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 74, 84, 94, 95, 85, 75, 76, 66, 56, 55, 54, 53, 43, 33, 23, 13, 14, 24, 34, 44, 45, 35, 25, 15, 16, 26, 36, 46, 47, 37, 27, 17, 57, 67, 77, 87, 86, 96, 97], "patrol_path_length": 60, "patrol_time": 652.0, "patrol_distance": 62.0, "return_path_sequence": [97, 96, 95, 94, 93, 92, 91], "return_path_length": 7, "return_time": 6.0, "return_distance": 6.0, "total_flight_time": 658.0, "computation_time_ms": 286.82827949523926, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [64, 65, 66], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 57, 47, 37, 27, 17, 67, 77, 76, 75, 74, 84, 94, 95, 85, 86, 96, 97, 87], "patrol_path_length": 60, "patrol_time": 653.0, "patrol_distance": 63.0, "return_path_sequence": [87, 86, 85, 84, 83, 82, 91], "return_path_length": 7, "return_time": 6.414213562373095, "return_distance": 6.414213562373095, "total_flight_time": 659.4142135623731, "computation_time_ms": 300.16422271728516, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [65, 66, 67], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 76, 86, 96, 97, 87, 77, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 57, 47, 37, 27, 17], "patrol_path_length": 60, "patrol_time": 650.8284271247462, "patrol_distance": 60.82842712474619, "return_path_sequence": [17, 26, 35, 44, 53, 62, 72, 81, 91], "return_path_length": 9, "return_time": 10.485281374238571, "return_distance": 10.485281374238571, "total_flight_time": 661.3137084989847, "computation_time_ms": 319.32830810546875, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [71, 72, 73], "patrol_path_sequence": [91, 81, 82, 92, 93, 83, 84, 74, 64, 63, 62, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 65, 75, 85, 95, 94, 96, 86, 76, 77, 67, 57, 47, 37, 27, 17, 87, 97], "patrol_path_length": 60, "patrol_time": 656.0, "patrol_distance": 66.0, "return_path_sequence": [97, 96, 95, 94, 93, 92, 91], "return_path_length": 7, "return_time": 6.0, "return_distance": 6.0, "total_flight_time": 662.0, "computation_time_ms": 299.3893623352051, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [72, 73, 74], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 75, 85, 84, 83, 82, 92, 93, 94, 95, 96, 86, 87, 77, 67, 57, 47, 37, 27, 17, 97], "patrol_path_length": 60, "patrol_time": 656.0, "patrol_distance": 66.0, "return_path_sequence": [97, 96, 95, 94, 93, 92, 91], "return_path_length": 7, "return_time": 6.0, "return_distance": 6.0, "total_flight_time": 662.0, "computation_time_ms": 333.58240127563477, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [73, 74, 75], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 84, 94, 95, 85, 86, 76, 66, 65, 64, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 57, 47, 37, 27, 17, 67, 77, 87, 97, 96], "patrol_path_length": 60, "patrol_time": 653.0, "patrol_distance": 63.0, "return_path_sequence": [96, 95, 94, 93, 92, 91], "return_path_length": 6, "return_time": 5.0, "return_distance": 5.0, "total_flight_time": 658.0, "computation_time_ms": 313.1694793701172, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [74, 75, 76], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 67, 57, 47, 37, 27, 17, 77, 87, 86, 85, 84, 94, 95, 96, 97], "patrol_path_length": 60, "patrol_time": 654.0, "patrol_distance": 64.0, "return_path_sequence": [97, 96, 95, 94, 93, 92, 91], "return_path_length": 7, "return_time": 6.0, "return_distance": 6.0, "total_flight_time": 660.0, "computation_time_ms": 331.58349990844727, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [75, 76, 77], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 86, 96, 97, 87, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 67, 57, 47, 37, 27, 17], "patrol_path_length": 60, "patrol_time": 650.8284271247462, "patrol_distance": 60.82842712474619, "return_path_sequence": [17, 26, 35, 44, 53, 62, 72, 81, 91], "return_path_length": 9, "return_time": 10.485281374238571, "return_distance": 10.485281374238571, "total_flight_time": 661.3137084989847, "computation_time_ms": 349.7898578643799, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [81, 82, 83], "patrol_path_sequence": [91, 92, 93, 94, 84, 74, 73, 72, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 75, 85, 95, 96, 86, 87, 77, 67, 57, 47, 37, 27, 17, 97], "patrol_path_length": 60, "patrol_time": 656.0, "patrol_distance": 66.0, "return_path_sequence": [97, 96, 95, 94, 93, 92, 91], "return_path_length": 7, "return_time": 6.0, "return_distance": 6.0, "total_flight_time": 662.0, "computation_time_ms": 315.40918350219727, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [82, 83, 84], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 86, 85, 95, 94, 93, 92, 96, 97, 87, 77, 67, 57, 47, 37, 27, 17], "patrol_path_length": 60, "patrol_time": 652.0, "patrol_distance": 62.0, "return_path_sequence": [17, 26, 35, 44, 53, 62, 71, 81, 91], "return_path_length": 9, "return_time": 10.485281374238571, "return_distance": 10.485281374238571, "total_flight_time": 662.4852813742385, "computation_time_ms": 319.7965621948242, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [83, 84, 85], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 94, 95, 96, 86, 76, 75, 74, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 67, 57, 47, 37, 27, 17, 77, 87, 97], "patrol_path_length": 60, "patrol_time": 654.0, "patrol_distance": 64.0, "return_path_sequence": [97, 96, 95, 94, 93, 92, 91], "return_path_length": 7, "return_time": 6.0, "return_distance": 6.0, "total_flight_time": 660.0, "computation_time_ms": 305.44376373291016, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [84, 85, 86], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 77, 67, 57, 47, 37, 27, 17, 87, 97, 96, 95, 94], "patrol_path_length": 60, "patrol_time": 655.0, "patrol_distance": 65.0, "return_path_sequence": [94, 93, 92, 91], "return_path_length": 4, "return_time": 3.0, "return_distance": 3.0, "total_flight_time": 658.0, "computation_time_ms": 310.8541965484619, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [85, 86, 87], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 96, 97, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 77, 67, 57, 47, 37, 27, 17], "patrol_path_length": 60, "patrol_time": 650.8284271247462, "patrol_distance": 60.82842712474619, "return_path_sequence": [17, 26, 35, 44, 53, 62, 72, 81, 91], "return_path_length": 9, "return_time": 10.485281374238571, "return_distance": 10.485281374238571, "total_flight_time": 661.3137084989847, "computation_time_ms": 309.83924865722656, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [92, 93, 94], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 86, 96, 95, 97, 87, 77, 67, 57, 47, 37, 27, 17], "patrol_path_length": 60, "patrol_time": 650.0, "patrol_distance": 60.0, "return_path_sequence": [17, 26, 35, 44, 53, 62, 72, 81, 91], "return_path_length": 9, "return_time": 10.485281374238571, "return_distance": 10.485281374238571, "total_flight_time": 660.4852813742385, "computation_time_ms": 306.4544200897217, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [93, 94, 95], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 86, 96, 97, 87, 77, 67, 57, 47, 37, 27, 17], "patrol_path_length": 60, "patrol_time": 649.4142135623731, "patrol_distance": 59.41421356237309, "return_path_sequence": [17, 26, 35, 44, 53, 62, 72, 81, 91], "return_path_length": 9, "return_time": 10.485281374238571, "return_distance": 10.485281374238571, "total_flight_time": 659.8994949366116, "computation_time_ms": 354.83789443969727, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [94, 95, 96], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 86, 87, 77, 67, 57, 47, 37, 27, 17, 97], "patrol_path_length": 60, "patrol_time": 656.0, "patrol_distance": 66.0, "return_path_sequence": [97, 87, 86, 85, 84, 83, 82, 91], "return_path_length": 8, "return_time": 7.414213562373095, "return_distance": 7.414213562373095, "total_flight_time": 663.4142135623731, "computation_time_ms": 334.9480628967285, "coverage_rate": 100.0, "is_valid": true}, {"no_fly_zones": [95, 96, 97], "patrol_path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 86, 87, 77, 67, 57, 47, 37, 27, 17], "patrol_path_length": 60, "patrol_time": 649.4142135623731, "patrol_distance": 59.41421356237309, "return_path_sequence": [17, 26, 35, 44, 53, 62, 72, 81, 91], "return_path_length": 9, "return_time": 10.485281374238571, "return_distance": 10.485281374238571, "total_flight_time": 659.8994949366116, "computation_time_ms": 308.50720405578613, "coverage_rate": 100.0, "is_valid": true}]}